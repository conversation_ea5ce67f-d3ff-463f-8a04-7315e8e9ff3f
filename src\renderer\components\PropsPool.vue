<template>
  <el-container :formKey="formKey">
    <ul class="levelcontainer">
      <li v-for="(value, key) in commonFallingconfigs" :key="key">
        <div class="" v-if="value.length">
          <span class="enemyLine" style="float: left">{{ value.name }}</span>
          <div
            class="enemyLine"
            style="float: left"
            v-for="idx in value.length"
            :key="idx"
          >
            <span class="enemyK">{{ idx }}</span>
            <el-input
              v-if="value.type === 'string'"
              class="enemyV"
              v-model="itemData[value.config][idx - 1]"
              style="width: 50%"
            ></el-input>

            <el-input-number
              v-else
              class="enemyV"
              v-model.number="itemData[value.config][idx - 1]"
              style="width: 50%"
            ></el-input-number>
          </div>
        </div>
        <div class="enemyLine" style="float: left" v-else>
          <span class="enemyK">{{ value.name }}</span>
          <el-input
            v-if="value.type === 'string'"
            class="enemyV"
            v-model="itemData[value.config]"
            style="width: 50%"
          ></el-input>

          <el-input-number
            v-else
            class="enemyV"
            v-model.number="itemData[value.config]"
            style="width: 50%"
          ></el-input-number>
        </div>
      </li>
      <li style="height: auto">
        <div class="itemname">添加道具池：</div>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addPropsPool"
        ></i>
        <el-collapse>
          <el-collapse-item v-for="(v1, k1) in itemData.PropsPools" :key="k1">
            <template class="itemname" slot="title">
              {{ k1 }}
              <i
                class="el-icon-plus"
                style="font-size: 25px; vertical-align: middle"
                @click="addPropsPoolFall(k1)"
              ></i>
              <i
                class="el-icon-delete"
                style="font-size: 25px; vertical-align: middle"
                @click="removePropsPool(k1)"
              ></i>
              <span class="enemyK">&emsp;&emsp;道具池概率：</span>
              <el-input-number
                class="enemyV"
                v-model="v1.propsPoolProbability"
                style="width: 30%"
              ></el-input-number>
            </template>
            <ul>
              <li
                v-for="(v2, k2) in v1.props"
                :key="k2"
                class="itemname"
                slot="title"
                style="height: auto"
              >
                <span>掉落物{{ k2 + 1 }}：</span>
                <i
                  class="el-icon-delete"
                  style="font-size: 25px; vertical-align: middle"
                  @click="removePropsPoolFall(k1, k2)"
                ></i>
                <ul>
                  <li>
                    <span class="enemyK">掉落物ID：</span>
                    <el-input
                      v-model="v2.fallID"
                      placeholder="请输入掉落物ID"
                      style="width: 50%"
                    ></el-input>
                  </li>
                  <li>
                    <span class="enemyK">掉落物名称：</span>
                    <el-input
                      v-model="v2.fallName"
                      placeholder="请输入掉落物名称"
                      style="width: 50%"
                    ></el-input>
                  </li>
                  <li>
                    <span class="enemyK">掉落概率：</span>
                    <el-input-number
                      class="enemyV"
                      v-model="v2.fallProbability"
                      style="width: 50%"
                    ></el-input-number>
                  </li>
                  <li>
                    <span class="enemyK">最小掉落数量：</span>
                    <el-input-number
                      class="enemyV"
                      v-model="v2.minFallNumber"
                      style="width: 50%"
                    ></el-input-number>
                  </li>
                  <li>
                    <span class="enemyK">最大掉落数量：</span>
                    <el-input-number
                      class="enemyV"
                      v-model="v2.maxFallNumber"
                      style="width: 50%"
                    ></el-input-number>
                  </li>
                </ul>
              </li>
            </ul>
          </el-collapse-item>
        </el-collapse>
      </li>
    </ul>
  </el-container>
</template>
<style>
.levelcontainer {
  list-style: none;
  float: left;
  text-align: left;
  padding-left: 0;
  width: 100%;
  vertical-align: middle;
}
.levelcontainer li {
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 5px 0;
}
.itemname {
  width: 150px;
  float: left;
}
.itemvalue {
  float: left;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.enemyK {
  width: 150px;
  float: left;
  height: 40px;
  overflow: auto;
}
.enemyV {
  width: 100px;
  float: left;
}
.enemyLine {
  width: 100%;
  height: 40px;
  line-height: 40px;
}
</style>
<script>
import Vue from "vue";
import globaldata from "../GlobalData.js";
export default {
  data() {
    return {
      formKey: "propsPool",
      curItem: "",
      itemData: {},
      commonFallingconfigs: [
        { name: "免费召唤冷却时间", config: "freeCallCoolDownTime" },
        { name: "每日可抽奖次数", config: "dailyCallNums" },
      ],
    };
  },
  components: {},
  mounted: function () {
    this.itemData = global.gamedata[this.formKey];
    console.log(this.itemData);
    if (!this.itemData) {
      var v = {};
      for (let k in this.commonFallingconfigs) {
        if (this.commonFallingconfigs[k]["length"]) {
          let n = [];
          for (let i = 0; i < this.commonFallingconfigs[k]["length"]; i++) {
            n.push(0);
          }
          v[this.commonFallingconfigs[k]["config"]] = n;
        } else {
          v[this.commonFallingconfigs[k]["config"]] = 0;
        }
      }
      Vue.set(global.gamedata, this.formKey, v);
      this.itemData = global.gamedata[this.formKey];
      //   if(this.itemData.Chapters){
      //       this.chapterNumber=this.itemData.Chapters.length;
      //   }
    }
  },
  methods: {
    addPropsPool: function () {
      if (!this.itemData.PropsPools) {
        Vue.set(this.itemData, "PropsPools", {});
      }
      this.$prompt("请输入道具池名称", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(({ value }) => {
          if (this.itemData.PropsPools.hasOwnProperty(value)) {
            this.$message({
              type: "info",
              message: "已存在同名道具池",
            });
          } else {
            Vue.set(this.itemData.PropsPools, value, {propsPoolProbability:0,props:[]});
            console.log("aaaaaaa",this.itemData.PropsPools[value]);
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消输入",
          });
        });
    },
    removePropsPool: function (idx) {
      Vue.delete(this.itemData.PropsPools,idx);
    },
    addPropsPoolFall: function (index) {
      let propsFall = {
        fallID: "",
        fallName:"",
        fallProbability: 0,
        minFallNumber: 0,
        maxFallNumber: 0,
      };
      this.itemData.PropsPools[index].props.push(propsFall);
    },
    removePropsPoolFall: function (index1, index2) {
      this.itemData.PropsPools[index1].props.splice(index2, 1);
    },
    forceUpdate: function () {
      this.$forceUpdate();
    },
    changes: function () {},
  },
};
</script>