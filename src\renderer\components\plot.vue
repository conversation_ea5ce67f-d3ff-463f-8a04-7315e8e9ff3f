<style>
.levelcontainer {
  list-style: none;
  float: left;
  text-align: left;
  padding-left: 0;
  width: 100%;
  vertical-align: middle;
}
.levelcontainer li {
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 5px 0;
}
.itemname {
  width: 150px;
  float: left;
}
.itemvalue {
  float: left;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.enemyK {
  width: 200px;
  float: left;
  height: 40px;
  overflow: auto;
}
.enemyV {
  width: 100px;
  float: left;
}
.enemyLine {
  width: 100%;
  height: 40px;
  line-height: 40px;
}
</style>
<template>
  <mylistview :formKey="formKey" @setItemData="getItemData">
    <ul class="levelcontainer">
      <li>
        <div class="itemname">剧情对话：</div>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addplot"
        ></i>
        <ul>
          <li
            v-for="(v1, k1) in itemData.plots"
            :key="k1"
            class="itemname"
            slot="title"
            style="height:220px"
          >
            <span>对话{{ k1 + 1 }}：</span>
            <i
              class="el-icon-delete"
              style="font-size: 25px; vertical-align: middle"
              @click="removeplot(k1)"
            ></i>
            <!-- <i class="el-icon-more"></i> -->
            <ul>
              <li>
                <span>左边对话角色：</span>
                <el-input
                  v-model="itemData.plots[k1].leftPlotRole"
                  placeholder="请输入角色ID"
                  style="width: 50%"
                ></el-input>
              </li>
              <li>
                <span>左边角色文本：</span>
                <el-input
                  v-model="itemData.plots[k1].leftPlotText"
                  placeholder="请输入对话文本"
                  style="width: 50%"
                ></el-input>
              </li>
              <li>
                <span>右边对话角色：</span>
                <el-input
                  v-model="itemData.plots[k1].rightPlotRole"
                  placeholder="请输入角色ID"
                  style="width: 50%"
                ></el-input>
              </li>
              <li>
                <span>右边角色文本：</span>
                <el-input
                  v-model="itemData.plots[k1].rightPlotText"
                  placeholder="请输入对话文本"
                  style="width: 50%"
                ></el-input>
              </li>
            </ul>
          </li>
        </ul>
      </li>
    </ul>
  </mylistview>
</template>
<script>
import mylistview from "./MyListView.vue";
import Vue from "vue";
export default {
  data() {
    return {
      formKey: "plot",
      curItem: "",
      itemData: {},
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      this.itemData = data;
    },
    addplot: function () {
      let plot = { leftPlotRole: "",rightPlotRole: "", leftPlotText: "",rightPlotText:"" };
      if (!this.itemData.plots || this.itemData.plots.length == 0) {
        Vue.set(this.itemData, "plots", [plot]);
      } else {
        this.itemData.plots.push(plot);
      }
    },
    removeplot: function (idx) {
      this.itemData.plots.splice(idx, 1);
    },
  },
};
</script>