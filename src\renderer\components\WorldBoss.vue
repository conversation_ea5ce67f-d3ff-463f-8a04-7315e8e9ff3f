<template>
  <mylistview :formKey="formKey" v-on:setItemData="getItemData">
    <div class="activitycontainer">
      <!-- 基本信息 -->
      <div class="form-row">
        <div class="itemname">ID:</div>
        <el-input class="itemvalue" v-model="itemData.id" placeholder="请输入世界BOSS ID"></el-input>
      </div>

      <div class="form-row">
        <div class="itemname">挑战次数:</div>
        <el-input-number class="itemvalue" v-model.number="itemData.challenges" :min="0" style="width: 200px"></el-input-number>
      </div>

      <div class="form-row">
        <div class="itemname">额外挑战次数:</div>
        <el-input-number class="itemvalue" v-model.number="itemData.addChallenges" :min="0" style="width: 200px"></el-input-number>
      </div>

      <div class="form-row">
        <div class="itemname">天数:</div>
        <el-input-number class="itemvalue" v-model.number="itemData.day" :min="1" style="width: 200px"></el-input-number>
      </div>

      <div class="form-row">
        <div class="itemname">伤害基数:</div>
        <el-input-number class="itemvalue" v-model.number="itemData.damageBase" :min="0" style="width: 200px"></el-input-number>
      </div>

      <div class="form-row">
        <div class="itemname">伤害增长值:</div>
        <el-input-number class="itemvalue" v-model.number="itemData.damageGrowth" :min="0" style="width: 200px"></el-input-number>
      </div>

      <div class="form-row">
        <div class="itemname">伤害上限:</div>
        <el-input-number class="itemvalue" v-model.number="itemData.damageLimit" :min="0" style="width: 200px"></el-input-number>
      </div>
      
      <!-- 伤害数组配置 -->
      <div class="gift-item">
        <div class="form-row">
          <h3 style="margin: 0;">排行榜伤害配置(单位:万)</h3>
        </div>
        <div class="damage-array-container">
          <div v-for="(damageItem, index) in itemData.damageArray" :key="index" class="damage-row">
            <span class="damage-level">第{{index + 1}}名:</span>
            <span class="damage-label">最高伤害:</span>
            <el-input-number v-model.number="damageItem.maxDamage" :min="0" size="small" style="width: 120px"></el-input-number>
            <span class="damage-label">最低伤害:</span>
            <el-input-number v-model.number="damageItem.minDamage" :min="0" size="small" style="width: 120px"></el-input-number>
          </div>
        </div>
      </div>
      
      <!-- 任务配置 -->
      <div class="gift-item">
        <div class="form-row">
          <h3 style="margin: 0;">任务配置</h3>
          <i class="el-icon-plus operation-btn" @click="addTask"></i>
        </div>
        <div v-for="(task, index) in itemData.task" :key="index" class="gift-item" style="margin: 10px 0;">
          <div class="form-row">
            <h4 style="margin: 0;">任务 {{index + 1}}</h4>
            <i class="el-icon-delete operation-btn" @click="removeTask(index)"></i>
          </div>
          
          <div class="form-row">
            <div class="form-label">任务说明:</div>
            <el-input v-model="task.taskExplain" @input="forceUpdate" placeholder="请输入任务说明" style="width: 300px"></el-input>
          </div>
          
          <div class="form-row">
            <div class="form-label">目标次数:</div>
            <el-input-number v-model.number="task.targetCount" @input="forceUpdate" :min="1" style="width: 150px"></el-input-number>
          </div>
          
          <div class="form-row">
            <div class="form-label">奖励内容:</div>
            <i class="el-icon-plus operation-btn" @click="addReward(task)"></i>
          </div>
          
          <div v-for="(reward, rewardIndex) in task.reward" :key="rewardIndex" class="reward-item">
            <el-select v-model="reward.id" clearable filterable allow-create placeholder="奖励ID" style="width: 200px;">
              <el-option v-for="(item, idx) in CommonlyArticles" :key="idx" :label="getArticleName(item)" :value="item" />
            </el-select>
            <el-input type="number" v-model.number="reward.count" placeholder="数量" style="width: 100px"></el-input>
            <i class="el-icon-delete operation-btn" @click="removeReward(task, rewardIndex)"></i>
            <span>{{getArticleName(reward.id)}}</span>
          </div>
        </div>
      </div>

      <!-- 排名奖励配置 -->
      <div class="gift-item">
        <div class="form-row">
          <h3 style="margin: 0;">排名奖励</h3>
          <i class="el-icon-plus operation-btn" @click="addRank"></i>
        </div>
        <div v-for="(rank, index) in itemData.rank" :key="index" class="gift-item" style="margin: 10px 0;">
          <div class="form-row">
            <h4 style="margin: 0;">排名 {{index + 1}}</h4>
            <i class="el-icon-delete operation-btn" @click="removeRank(index)"></i>
          </div>
          
          <div class="form-row">
            <div class="form-label">目标排名:</div>
            <el-input-number v-model.number="rank.targetRank" @input="forceUpdate" :min="1" style="width: 150px"></el-input-number>
          </div>
          
          <div class="form-row">
            <div class="form-label">排名奖励:</div>
            <i class="el-icon-plus operation-btn" @click="addRankReward(rank)"></i>
          </div>
          
          <div v-for="(reward, rewardIndex) in rank.rewardRank" :key="rewardIndex" class="reward-item">
            <el-select v-model="reward.id" clearable filterable allow-create placeholder="奖励ID" style="width: 200px;">
              <el-option v-for="(item, idx) in CommonlyArticles" :key="idx" :label="getArticleName(item)" :value="item" />
            </el-select>
            <el-input type="number" v-model.number="reward.count" placeholder="数量" style="width: 100px"></el-input>
            <i class="el-icon-delete operation-btn" @click="removeRankReward(rank, rewardIndex)"></i>
            <span>{{getArticleName(reward.id)}}</span>
          </div>
        </div>
      </div>

      <!-- BOSS配置 -->
      <div class="gift-item">
        <div class="form-row">
          <h3 style="margin: 0;">BOSS配置</h3>
          <i class="el-icon-plus operation-btn" @click="addBoss"></i>
        </div>
        <div v-for="(boss, index) in itemData.boss" :key="index" class="gift-item" style="margin: 10px 0;">
          <div class="form-row">
            <h4 style="margin: 0;">BOSS {{index + 1}}</h4>
            <i class="el-icon-delete operation-btn" @click="removeBoss(index)"></i>
          </div>
          
          <div class="form-row">
            <div class="form-label">BOSS ID:</div>
            <el-input v-model="boss.bossID" @input="forceUpdate" placeholder="请输入BOSS ID" style="width: 200px"></el-input>
          </div>
          
          <div class="form-row">
            <div class="form-label">伤害倍率:</div>
            <el-input-number v-model.number="boss.damage" @input="forceUpdate" :min="0.1" :precision="2" :step="0.1" style="width: 150px"></el-input-number>
          </div>
        </div>
      </div>
    </div>
  </mylistview>
</template>

<style>
.activitycontainer {
    list-style: none;
    padding-left: 0;
    width: 100%;
    overflow: hidden;
}

.activitycontainer li {
    width: 100%;
    min-height: 40px;
    margin: 5px 0;
    clear: both;
}

.itemname {
    width: 150px;
    float: left;
    line-height: 40px;
}

.itemvalue {
    width: 50%;
    float: left;
}

/* 礼包项样式 */
.gift-item {
    border: 1px solid #eee;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    width: 100%;
}

/* 表单项样式 */
.form-row {
    display: flex;
    align-items: center;
    margin: 10px 0;
}

.form-label {
    width: 80px;
    flex-shrink: 0;
}

/* 奖励项样式 */
.reward-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 5px 0;
}

/* 操作按钮样式 */
.operation-btn {
    margin-left: 10px;
    cursor: pointer;
}

/* 伤害数组样式 */
.damage-array-container {
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 10px;
    background: #fafafa;
}

.damage-row {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    gap: 10px;
}

.damage-row:last-child {
    border-bottom: none;
}

.damage-level {
    font-weight: bold;
    color: #333;
    min-width: 60px;
}

.damage-label {
    color: #666;
    font-size: 14px;
    min-width: 70px;
}
</style>

<script>
import Vue from 'vue';
import mylistview from "./MyListView.vue";

export default {
  data() {
    return {
      formKey: "worldBoss",
      CommonlyArticles: [],
      itemData: {},
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      if (data) {
        this.itemData = data;
      } else {
        this.itemData = { 
          task: [],
          rank: [],
          boss: [],
          damageArray: []
        };
      }
      this.CommonlyArticles = global.gamedata["CommonlyArticles"] || [];
      
      // 确保数组字段存在
      if (!this.itemData.task) {
        Vue.set(this.itemData, 'task', []);
      }
      if (!this.itemData.rank) {
        Vue.set(this.itemData, 'rank', []);
      }
      if (!this.itemData.boss) {
        Vue.set(this.itemData, 'boss', []);
      }
      if (!this.itemData.damageArray) {
        Vue.set(this.itemData, 'damageArray', []);
      }
      
      // 确保伤害数组长度为20
      if (this.itemData.damageArray.length < 20) {
        for (let i = this.itemData.damageArray.length; i < 20; i++) {
          this.itemData.damageArray.push({
            maxDamage: 0,
            minDamage: 0
          });
        }
      }
      console.log("ItemData", this.itemData);
    },
    forceUpdate: function () {
      this.$forceUpdate();
    },
    // 任务相关方法
    addTask: function() {
      if (!this.itemData.task) {
        this.itemData.task = [];
      }
      this.itemData.task.push({
        taskExplain: "",
        targetCount: 1,
        reward: []
      });
      this.$forceUpdate();
    },
    removeTask: function(index) {
      this.itemData.task.splice(index, 1);
      this.$forceUpdate();
    },
    addReward: function(task) {
      if (!task.reward) {
        task.reward = [];
      }
      task.reward.push({
        id: "",
        count: 1
      });
      this.$forceUpdate();
    },
    removeReward: function(task, index) {
      task.reward.splice(index, 1);
      this.$forceUpdate();
    },
    // 排名相关方法
    addRank: function() {
      if (!this.itemData.rank) {
        this.itemData.rank = [];
      }
      this.itemData.rank.push({
        targetRank: 1,
        rewardRank: []
      });
      this.$forceUpdate();
    },
    removeRank: function(index) {
      this.itemData.rank.splice(index, 1);
      this.$forceUpdate();
    },
    addRankReward: function(rank) {
      if (!rank.rewardRank) {
        rank.rewardRank = [];
      }
      rank.rewardRank.push({
        id: "",
        count: 1
      });
      this.$forceUpdate();
    },
    removeRankReward: function(rank, index) {
      rank.rewardRank.splice(index, 1);
      this.$forceUpdate();
    },
    // BOSS相关方法
    addBoss: function() {
      if (!this.itemData.boss) {
        this.itemData.boss = [];
      }
      this.itemData.boss.push({
        bossID: "",
        damage: 1.0
      });
      this.$forceUpdate();
    },
    removeBoss: function(index) {
      this.itemData.boss.splice(index, 1);
      this.$forceUpdate();
    },
    getArticleName(articleId) {
      let _article = global.gamedata["article"][articleId];
      if (!_article) return "";
      return _article.name;
    }
  },
};
</script>
