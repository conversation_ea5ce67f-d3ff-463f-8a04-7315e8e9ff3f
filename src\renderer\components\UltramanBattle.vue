<template>
  <mylistview :formKey="formKey" v-on:setItemData="getItemData">
    <div class="activitycontainer">
      <!-- 活动物品配置 -->
      <div class="gift-item">
        <div class="form-row">
          <h3 style="margin: 0;">活动物品配置</h3>
          <i class="el-icon-plus operation-btn" @click="addActivityItem"></i>
        </div>
        <div class="item-array-container">
          <div v-for="(_, index) in itemData.itemArr" :key="index" class="item-row">
            <el-input v-model="itemData.itemArr[index]" placeholder="请输入活动物品ID" style="width: 200px;"></el-input>
            <i class="el-icon-delete operation-btn" @click="removeActivityItem(index)"></i>
          </div>
        </div>
      </div>
      
      <!-- 任务配置 -->
      <div class="gift-item">
        <div class="form-row">
          <h3 style="margin: 0;">任务配置</h3>
          <i class="el-icon-plus operation-btn" @click="addTask"></i>
        </div>
        <div v-for="(task, index) in itemData.task" :key="index" class="gift-item" style="margin: 10px 0;">
          <div class="form-row">
            <h4 style="margin: 0;">任务 {{index + 1}}</h4>
            <i class="el-icon-delete operation-btn" @click="removeTask(index)"></i>
          </div>
          
          <div class="form-row">
            <div class="form-label">任务描述:</div>
            <el-input v-model="task.taskExplain" @input="forceUpdate" placeholder="请输入任务描述" style="width: 300px"></el-input>
          </div>
          
          <div class="form-row">
            <div class="form-label">目标数量:</div>
            <el-input-number v-model.number="task.targetCount" @input="forceUpdate" :min="1" style="width: 150px"></el-input-number>
          </div>
          
          <div class="form-row">
            <div class="form-label">奖励物品ID:</div>
            <el-select v-model="task.rewardId" clearable filterable allow-create placeholder="奖励物品ID" style="width: 200px;">
              <el-option v-for="(item, idx) in itemData.itemArr" :key="idx" :label="getArticleName(item)" :value="item" />
            </el-select>
            <span>{{getArticleName(task.rewardId)}}</span>
          </div>
          
          <div class="form-row">
            <div class="form-label">奖励数量:</div>
            <el-input-number v-model.number="task.rewardNum" @input="forceUpdate" :min="1" style="width: 150px"></el-input-number>
          </div>
        </div>
      </div>

      <!-- 兑换配置 -->
      <div class="gift-item">
        <div class="form-row">
          <h3 style="margin: 0;">兑换配置</h3>
          <i class="el-icon-plus operation-btn" @click="addExchange"></i>
        </div>
        <div v-for="(exchange, index) in itemData.exchange" :key="index" class="gift-item" style="margin: 10px 0;">
          <div class="form-row">
            <h4 style="margin: 0;">兑换项 {{index + 1}}</h4>
            <i class="el-icon-delete operation-btn" @click="removeExchange(index)"></i>
          </div>
          
          <div class="form-row">
            <div class="form-label">兑换物品ID:</div>
            <el-select v-model="exchange.id" clearable filterable allow-create placeholder="兑换物品ID" style="width: 200px;">
              <el-option v-for="(article, idx) in CommonlyArticles" :key="idx" :label="getArticleName(article)" :value="article" />
            </el-select>
            <span>{{getArticleName(exchange.id)}}</span>
          </div>
          
          <div class="form-row">
            <div class="form-label">兑换数量:</div>
            <el-input-number v-model.number="exchange.count" @input="forceUpdate" :min="1" style="width: 150px"></el-input-number>
          </div>
          
          <div class="form-row">
            <div class="form-label">可兑换次数:</div>
            <el-input-number v-model.number="exchange.exchangeCount" @input="forceUpdate" :min="1" style="width: 150px"></el-input-number>
          </div>
          
          <div class="form-row">
            <div class="form-label">兑换需求:</div>
            <i class="el-icon-plus operation-btn" @click="addExchangeRequirement(exchange)"></i>
          </div>
          
          <div v-for="(requirement, reqIndex) in exchange.rewardExchange" :key="reqIndex" class="reward-item">
            <el-select v-model="requirement.id" clearable filterable allow-create placeholder="需求物品ID" style="width: 200px;">
              <el-option v-for="(item, idx) in itemData.itemArr" :key="idx" :label="getArticleName(item)" :value="item" />
            </el-select>
            <el-input type="number" v-model.number="requirement.count" placeholder="数量" style="width: 100px"></el-input>
            <i class="el-icon-delete operation-btn" @click="removeExchangeRequirement(exchange, reqIndex)"></i>
            <span>{{getArticleName(requirement.id)}}</span>
          </div>
        </div>
      </div>

      <!-- 商城配置 -->
      <div class="gift-item">
        <div class="form-row">
          <h3 style="margin: 0;">商城配置</h3>
          <i class="el-icon-plus operation-btn" @click="addStore"></i>
        </div>
        <div v-for="(store, index) in itemData.store" :key="index" class="gift-item" style="margin: 10px 0;">
          <div class="form-row">
            <h4 style="margin: 0;">商城项 {{index + 1}}</h4>
            <i class="el-icon-delete operation-btn" @click="removeStore(index)"></i>
          </div>

          <div class="form-row">
            <div class="form-label">购买类型:</div>
            <el-select v-model="store.buyType" @change="forceUpdate" style="width: 150px;">
              <el-option label="免费" :value="0"></el-option>
              <el-option label="广告" :value="1"></el-option>
              <el-option label="人民币" :value="2"></el-option>
            </el-select>
          </div>

          <div class="form-row" v-if="store.buyType === 2">
            <div class="form-label">价格:</div>
            <el-input-number v-model.number="store.price" @input="forceUpdate" :min="0" :precision="2" style="width: 150px"></el-input-number>
          </div>

          <div class="form-row">
            <div class="form-label">限购次数:</div>
            <el-input-number v-model.number="store.buyCount" @input="forceUpdate" :min="1" style="width: 150px"></el-input-number>
          </div>

          <div class="form-row">
            <div class="form-label">商城物品:</div>
            <i class="el-icon-plus operation-btn" @click="addStoreReward(store)"></i>
          </div>

          <div v-for="(reward, rewardIndex) in store.rewardStore" :key="rewardIndex" class="reward-item">
            <el-select v-model="reward.ID" clearable filterable allow-create placeholder="物品ID" style="width: 200px;">
              <el-option v-for="(article, idx) in CommonlyArticles" :key="idx" :label="getArticleName(article)" :value="article" />
              <el-option v-for="(item, idx) in itemData.itemArr" :key="'item_' + idx" :label="getArticleName(item)" :value="item" />
            </el-select>
            <el-input type="number" v-model.number="reward.count" placeholder="数量" style="width: 100px"></el-input>
            <i class="el-icon-delete operation-btn" @click="removeStoreReward(store, rewardIndex)"></i>
            <span>{{getArticleName(reward.ID)}}</span>
          </div>
        </div>
      </div>

      <!-- 数据导出 -->
      <div class="gift-item">
        <div class="form-row">
          <h3 style="margin: 0;">数据导出</h3>
          <el-button type="primary" @click="showFormattedData">显示格式化数据</el-button>
          <el-button type="success" @click="exportJsonFile">导出JSON数据</el-button>
        </div>
        <div class="form-row">
          <el-input
            type="textarea"
            v-model="exportedData"
            placeholder="格式化的配置数据将显示在这里"
            :rows="10"
            readonly
            style="width: 100%;">
          </el-input>
        </div>
      </div>
    </div>
  </mylistview>
</template>

<style>
.activitycontainer {
    list-style: none;
    padding-left: 0;
    width: 100%;
    overflow: hidden;
}

.activitycontainer li {
    width: 100%;
    min-height: 40px;
    margin: 5px 0;
    clear: both;
}

.itemname {
    width: 150px;
    float: left;
    line-height: 40px;
}

.itemvalue {
    width: 50%;
    float: left;
}

/* 礼包项样式 */
.gift-item {
    border: 1px solid #eee;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    width: 100%;
}

/* 表单项样式 */
.form-row {
    display: flex;
    align-items: center;
    margin: 10px 0;
}

.form-label {
    width: 100px;
    flex-shrink: 0;
}

/* 奖励项样式 */
.reward-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 5px 0;
}

/* 操作按钮样式 */
.operation-btn {
    margin-left: 10px;
    cursor: pointer;
}

/* 活动物品数组样式 */
.item-array-container {
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 10px;
    background: #fafafa;
}

.item-row {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    gap: 10px;
}

.item-row:last-child {
    border-bottom: none;
}
</style>

<script>
import Vue from 'vue';
import mylistview from "./MyListView.vue";

const fs = require('fs');
const ele = require('electron');

export default {
  data() {
    return {
      formKey: "ultramanBattle",
      CommonlyArticles: [],
      itemData: {},
      exportedData: "",
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      if (data) {
        this.itemData = data;
      } else {
        this.itemData = {
          itemArr: [],
          task: [],
          exchange: [],
          store: []
        };
      }
      this.CommonlyArticles = global.gamedata["CommonlyArticles"] || [];
      
      // 确保数组字段存在
      if (!this.itemData.itemArr) {
        Vue.set(this.itemData, 'itemArr', []);
      }
      if (!this.itemData.task) {
        Vue.set(this.itemData, 'task', []);
      }
      if (!this.itemData.exchange) {
        Vue.set(this.itemData, 'exchange', []);
      }
      if (!this.itemData.store) {
        Vue.set(this.itemData, 'store', []);
      }
      
      console.log("UltramanBattle ItemData", this.itemData);
    },
    forceUpdate: function () {
      this.$forceUpdate();
    },
    // 活动物品相关方法
    addActivityItem: function() {
      this.itemData.itemArr.push("");
      this.$forceUpdate();
    },
    removeActivityItem: function(index) {
      this.itemData.itemArr.splice(index, 1);
      this.$forceUpdate();
    },
    // 任务相关方法
    addTask: function() {
      this.itemData.task.push({
        taskExplain: "",
        targetCount: 1,
        rewardId: "",
        rewardNum: 1
      });
      this.$forceUpdate();
    },
    removeTask: function(index) {
      this.itemData.task.splice(index, 1);
      this.$forceUpdate();
    },
    // 兑换相关方法
    addExchange: function() {
      this.itemData.exchange.push({
        id: "",
        count: 1,
        exchangeCount: 1,
        rewardExchange: []
      });
      this.$forceUpdate();
    },
    removeExchange: function(index) {
      this.itemData.exchange.splice(index, 1);
      this.$forceUpdate();
    },
    addExchangeRequirement: function(exchange) {
      if (!exchange.rewardExchange) {
        exchange.rewardExchange = [];
      }
      exchange.rewardExchange.push({
        id: "",
        count: 1
      });
      this.$forceUpdate();
    },
    removeExchangeRequirement: function(exchange, index) {
      exchange.rewardExchange.splice(index, 1);
      this.$forceUpdate();
    },
    // 商城相关方法
    addStore: function() {
      this.itemData.store.push({
        buyType: 0,
        price: 0,
        buyCount: 1,
        rewardStore: []
      });
      this.$forceUpdate();
    },
    removeStore: function(index) {
      this.itemData.store.splice(index, 1);
      this.$forceUpdate();
    },
    addStoreReward: function(store) {
      if (!store.rewardStore) {
        store.rewardStore = [];
      }
      store.rewardStore.push({
        ID: "",
        count: 1
      });
      this.$forceUpdate();
    },
    removeStoreReward: function(store, index) {
      store.rewardStore.splice(index, 1);
      this.$forceUpdate();
    },
    getArticleName(articleId) {
      let _article = global.gamedata["article"][articleId];
      if (!_article) return "";
      return _article.name;
    },
    // 显示格式化数据方法
    showFormattedData: function() {
      // 创建导出数据对象，移除不需要的字段
      const exportObj = {
        itemArr: this.itemData.itemArr || [],
        task: this.itemData.task || [],
        exchange: this.itemData.exchange || [],
        store: this.itemData.store || []
      };

      // 格式化JSON输出
      this.exportedData = JSON.stringify(exportObj, null, 2);

      // 提示用户
      this.$message.success('格式化数据已显示在下方文本框');
    },
    // 导出JSON文件
    exportJsonFile: function() {
      // 创建导出数据对象
      const exportObj = {
        itemArr: this.itemData.itemArr || [],
        task: this.itemData.task || [],
        exchange: this.itemData.exchange || [],
        store: this.itemData.store || []
      };

      // 使用Electron的dialog选择保存路径
      ele.remote.dialog.showSaveDialog({
        title: "导出奥特大作战配置",
        buttonLabel: '导出',
        nameFieldLabel: '请输入文件名',
        defaultPath: "ultramanBattle.json",
        filters: [{ name: 'JSON文件', extensions: ['json'] }]
      }, (filename) => {
        if (filename) {
          // 写入文件
          fs.writeFile(filename, JSON.stringify(exportObj), 'utf-8', (err) => {
            if (err) {
              console.log("导出文件失败", filename, err);
              this.$message.error('导出失败: ' + err.message);
              return;
            }
            // 显示成功通知
            (new ele.remote.Notification({
              title: "导出成功",
              body: "奥特大作战配置已导出到: " + filename
            })).show();
            this.$message.success('配置文件导出成功');
          });
        }
      });
    }
  },
};
</script>
