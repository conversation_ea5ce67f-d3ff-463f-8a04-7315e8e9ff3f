<style>
.levelcontainer {
  list-style: none;
  float: left;
  text-align: left;
  padding-left: 0;
  width: 100%;
  vertical-align: middle;
}
.levelcontainer li {
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 5px 0;
}
.itemname {
  width: 150px;
  float: left;
}
.itemvalue {
  float: left;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.enemyK {
  width: 200px;
  float: left;
  height: 40px;
  overflow: auto;
}
.enemyV {
  width: 100px;
  float: left;
}
.enemyLine {
  width: 100%;
  height: 40px;
  line-height: 40px;
}
</style>
<template>
    <mylistview :formKey="formKey" @setItemData="getItemData">
        <ul class="levelcontainer">
            <li v-for="(value, key) in vipPrivilegeConfig" :key="key">
                <span class="enemyK">
                    {{ value.name }}
                </span>
                <div class="enemyLine" v-if="(value.type=='bool')">
                    <template>
                        <el-radio v-model="itemData[value.config]" label="true">是</el-radio>
                        <el-radio v-model="itemData[value.config]" label="false">否</el-radio>
                    </template>
                </div>
                <div class="enemyLine" v-else>
                    <el-input
                        v-model.number="itemData[value.config]"
                        type="number"
                        class="enemyV"
                        style="width: 50%"
                    >
                    </el-input>
                </div>
            </li>
            <li>
                <div class="itemname">可领取物品：</div>
                <i 
                class="el-icon-plus"
                style="font-size: 25px; vertical-align: middle"
                @click="addVIPFree()"></i>
                <ul>
                    <li v-for="(v1, k1) in itemData.vipFreeGoodsIDs" :key="k1" class="itemname" slot="title">
                        <div class="enemyLine">
                            <span>可获得物品{{k1+1}}：</span>
                            <el-input
                                v-model="itemData.vipFreeGoodsIDs[k1].name"
                                placeholder="请输入物品ID"
                                style="width: 30%"
                            ></el-input>
                            <span>物品数量{{k1+1}}：</span>
                            <el-input
                                v-model.number="itemData.vipFreeGoodsIDs[k1].number"
                                type="number"
                                placeholder="请输入物品数量"
                                style="width: 30%"
                            ></el-input>
                            <i
                                class="el-icon-delete"
                                style="font-size: 25px; vertical-align: middle"
                                @click="removeVIPFree(k1)"
                            ></i>
                        </div>
                    </li>
                </ul>
            </li>
            <li>
                <div class="itemname">抢购物品：</div>
                <i 
                class="el-icon-plus"
                style="font-size: 25px; vertical-align: middle"
                @click="addVIPBuy()"></i>
                <ul>
                    <li v-for="(v1, k1) in itemData.vipBuyGoodsIDs" :key="k1" class="itemname" slot="title">
                        <span>可抢购物品{{k1+1}}：</span>
                        <el-input
                            v-model="itemData.vipBuyGoodsIDs[k1].name"
                            placeholder="请输入物品ID"
                            style="width: 30%"
                        ></el-input>
                        <span>物品数量{{k1+1}}：</span>
                        <el-input
                            v-model.number="itemData.vipBuyGoodsIDs[k1].number"
                            type="number"
                            placeholder="请输入物品数量"
                            style="width: 30%"
                        ></el-input>
                        <i
                            class="el-icon-delete"
                            style="font-size: 25px; vertical-align: middle"
                            @click="removeVIPBuy(k1)"
                        ></i>
                    </li>
                </ul>
            </li>
        </ul>
    </mylistview>
</template>
<script>
import mylistview from "./MyListView.vue";
import Vue from "vue";
const boolType=["是","否"];
export default {
  data() {
    return {
      formKey: "vipPrivilege",
      curItem: "",
      itemData: {},
      boolTypes:boolType,
      vipPrivilegeConfig: [
        // { name: "体力上限额外增加", config: "powerLimitIncrease"},
        // { name: "关卡经验获取增加", config: "levelExpIncrease" },
        // { name: "关卡金币获取增加", config: "levelGoldIconIncrease"},
        // { name: "每日体力药剂使用次数", config: "dayPowerDrugUseNumber"},
        // { name: "在线奖励收益翻倍", config: "onlineProfitDouble",type:"bool"},
        // { name: "每日竞技场挑战可购买次数", config: "dayArenaChallengeBuyNumber"},
        // { name: "每日可刷新皮肤商城次数", config: "dayRefreshSkinShopNumber"},
        // { name: "每日无限挑战次数", config: "dayLimitlessChallengeNumber"},
        // { name: "每日可上传云存档次数", config: "dayUpLoadSaveFileNumber"},
        // { name: "每日可优惠兑换金币次数", config: "dayDiscountExchangeGoldIconNumber"},
        // { name: "每月签到收益翻倍", config: "mouthSignInProfitDouble",type:"bool"},
        // { name: "每日火花塔挑战增加次数", config: "daySparkTowerChallengeIncNumber"},
        // { name: "开启关卡扫荡", config: "levelSweep",type:"bool"},
        // { name: "每日宇宙警备队挑战增加次数", config: "dayUniversePoliceForcechallengeIncNumber"},
        // { name: "每月可下载云存档次数", config: "mouthDownLoadSaveFileNumber"},
        // 
        {name: "充值金额", config: "rechargeAmount"},
        {name: "可自动战斗", config: "autoAttack",type:"bool"},
        {name: "精力基础值", config: "energyBaseValue"},
        {name: "经验获取率", config: "ExpGetValue"},
        {name: "金币获取率", config: "goldCoinGetValue"},
        {name: "云存档次数", config: "UpLoadSaveFileNum"},
        {name: "体力药剂使用次数", config: "powerDrugUseNum"},
        {name: "商城金币兑换次数", config: "shopGoldCoinExchangeNum"},
        {name: "英雄碎片获取次数", config: "heroDebirsGetNums"},
        {name: "远征挑战次数", config: "expeditionChanllengeNum"},
        {name: "远征广告次数", config: "expeditionChanllengeAdvNum"},
        {name: "火花挑战次数", config: "SparkTowerChallengeNum"},
        {name: "火花广告次数", config: "SparkTowerChallengeAdvNum"},
        {name: "超级Boss挑战次数", config: "superBossChallengeNum"},
        {name: "超级Boss广告次数", config: "superBossChallengeAdvNum"},
        {name: "宇宙监狱挑战次数", config: "universePrisonChallengeNum"},
        {name: "宇宙监狱广告次数", config: "universePrisonChallengeAdvNum"},
        { name: "超值免费获取总价值", config: "freeTotalGetValue"},
        { name: "超值购买总价", config: "BuyTotalValue"},
        { name: "超值购买现价", config: "BuyTotalNowValue"},

      ],
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      this.itemData = data;
      if(!this.itemData.rechargeAmount){
        Vue.set(this.itemData,"rechargeAmount",0);
      }
      if(!this.itemData.autoAttack){
        Vue.set(this.itemData,"autoAttack","false");
      }
      if(!this.itemData.energyBaseValue){
        Vue.set(this.itemData,"energyBaseValue",180);
      }
      if(!this.itemData.ExpGetValue){
        Vue.set(this.itemData,"ExpGetValue",1);
      }
      if(!this.itemData.goldCoinGetValue){
        Vue.set(this.itemData,"goldCoinGetValue",1);
      }
      if(!this.itemData.UpLoadSaveFileNum){
        Vue.set(this.itemData,"UpLoadSaveFileNum",0);
      }
      if(!this.itemData.powerDrugUseNum){
        Vue.set(this.itemData,"powerDrugUseNum",3);
      }
      if(!this.itemData.shopGoldCoinExchangeNum){
        Vue.set(this.itemData,"shopGoldCoinExchangeNum",3);
      }
      if(!this.itemData.heroDebirsGetNums){
        Vue.set(this.itemData,"heroDebirsGetNums",2);
      }
      if(!this.itemData.expeditionChanllengeNum){
        Vue.set(this.itemData,"expeditionChanllengeNum",2);
      }
      if(!this.itemData.expeditionChanllengeAdvNum){
        Vue.set(this.itemData,"expeditionChanllengeAdvNum",1);
      }
      if(!this.itemData.SparkTowerChallengeNum){
        Vue.set(this.itemData,"SparkTowerChallengeNum",2);
      }
      if(!this.itemData.SparkTowerChallengeAdvNum){
        Vue.set(this.itemData,"SparkTowerChallengeAdvNum",1);
      }
      if(!this.itemData.superBossChallengeNum){
        Vue.set(this.itemData,"superBossChallengeNum",1);
      }
      if(!this.itemData.superBossChallengeAdvNum){
        Vue.set(this.itemData,"superBossChallengeAdvNum",1);
      }
      if(!this.itemData.universePrisonChallengeNum){
        Vue.set(this.itemData,"universePrisonChallengeNum",1)
      }
      if(!this.itemData.universePrisonChallengeAdvNum){
        Vue.set(this.itemData,"universePrisonChallengeAdvNum",1)
      }
    },
    addVIPFree:function(){
      let vipFreeGoodsIdAndNumber={name:"",number:Number}
      if(!this.itemData.vipFreeGoodsIDs || this.itemData.vipFreeGoodsIDs.length == 0){
        Vue.set(this.itemData,"vipFreeGoodsIDs",[vipFreeGoodsIdAndNumber]);
      }
      else{
        this.itemData.vipFreeGoodsIDs.push(vipFreeGoodsIdAndNumber);
      }
    },
    removeVIPFree:function(idx){
      this.itemData.vipFreeGoodsIDs.splice(idx, 1);
    },
    addVIPBuy:function(){
      let vipBuyGoodsIdAndNumber={name:"",number:Number}
      if(!this.itemData.vipBuyGoodsIDs || this.itemData.vipBuyGoodsIDs.length == 0){
        Vue.set(this.itemData,"vipBuyGoodsIDs",[vipBuyGoodsIdAndNumber]);
      }
      else{
        this.itemData.vipBuyGoodsIDs.push(vipBuyGoodsIdAndNumber);
      }
    },
    removeVIPBuy:function(idx){
      this.itemData.vipBuyGoodsIDs.splice(idx, 1);
    }
  },
};
</script>