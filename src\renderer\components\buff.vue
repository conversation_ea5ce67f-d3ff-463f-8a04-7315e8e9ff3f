<template>
  <mylistview :formKey="formKey" v-on:setItemData="getItemData">
    <ul class="levelcontainer">
      <li>
        <div class="enemyLine">
          <span class="enemyK">名字:</span>
          <el-input
            class="enemyV"
            v-model="itemData.name"
            placeholder="请输入道具名称"
            style="width: 50%"
          ></el-input>
        </div>
      </li>
      <li>
        <div class="enemyLine">
          <span class="enemyK">描述:</span>
          <el-input
            class="enemyV"
            v-model="itemData.desc"
            placeholder="请输Buff描述"
            style="width: 50%"
          ></el-input>
        </div>
      </li>
      <li>
        <span class="enemyK">场景显示Icon</span>
        <template>
          <el-radio-group v-model="itemData.isSceneShow">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </template>
      </li>
      <li>
        <div class="itemname">类型:</div>
        <el-dropdown class="itemvalue" @command="selectTypeBox">
          <span class="el-dropdown-link">
            {{ typeStr }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(value, idx) in buffType"
              :command="idx"
              :key="idx"
              >{{ value }}</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </li>
      <li>
        <div class="itemname">触发方式:</div>
        <el-dropdown class="itemvalue" @command="selectTriggerBox">
          <span class="el-dropdown-link">
            {{ triggerStr }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(value, idx) in bufftrigger"
              :command="idx"
              :key="idx"
              >{{ value }}</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </li>
      <li>
        <div class="enemyLine">
          <span class="enemyK">持续时间（秒）:</span>
          <el-input-number
            class="enemyV"
            v-model.number="itemData.time"
            placeholder="请输入数值"
            style="width: 50%"
          ></el-input-number>
        </div>
      </li>
      <li>
        <div class="enemyLine">
          <span class="enemyK">触发几率（百分比）:</span>
          <el-input-number
            class="enemyV"
            v-model.number="itemData.probability"
            :min="0"
            :max="1"
            placeholder="请输入数值"
            style="width: 50%"
          ></el-input-number>
        </div>
      </li>
      <li
        v-if="
          typeStr != '眩晕' &&
          typeStr != '无敌' &&
          typeStr != '复活' &&
          typeStr != '多属性并存'&&
          typeStr != '沉默'
        "
      >
        <div class="enemyLine">
          <span class="enemyK">增量数值:</span>
          <el-input-number
            class="enemyV"
            v-model.number="itemData.value"
            placeholder="请输入数值"
            style="width: 50%"
          ></el-input-number>
        </div>
      </li>
      <li v-if="typeStr == '多属性并存'">
        <div class="enemyLine">
          <span class="enemyK">增量数值（百分比）:</span>
          <el-input
            class="enemyV"
            v-model="itemData.blendvalue"
            placeholder="多个以-隔开"
            style="width: 50%"
          ></el-input>
        </div>
      </li>
      <li v-if="typeStr == '持续回血' || typeStr == '持续伤害'">
        <div class="enemyLine">
          <span class="enemyK">间隔时间（秒）:</span>
          <el-input-number
            class="enemyV"
            v-model.number="itemData.dot"
            placeholder="请输入数值"
            style="width: 50%"
          ></el-input-number>
        </div>
      </li>
      <li v-if="triggerStr == '目标血量低于百分比'">
        <div class="enemyLine">
          <span class="enemyK">触发的百分比:</span>
          <el-input-number
            class="enemyV"
            :min="0"
            :max="1"
            v-model.number="itemData.t_p"
            placeholder="请输入数值"
            style="width: 50%"
          ></el-input-number>
        </div>
      </li>
      <li
        v-if="
          typeStr == '随机增强属性' ||
          typeStr == '随机削弱属性' ||
          typeStr == '多属性并存'
        "
      >
        <div class="enemyLine">
          <span class="enemyK">触发的buff（id）:</span>
          <el-input
            class="enemyV"
            v-model="itemData.bs"
            placeholder="多个以-隔开"
            style="width: 50%"
          ></el-input>
        </div>
      </li>
    </ul>
  </mylistview>
</template>
<script>
import mylistview from "./MyListView.vue";
import Vue from "vue";
const BuffType = [
  "增强攻击",
  "降低攻击",
  "增强防御",
  "降低防御",
  "增强闪避",
  "降低闪避",
  "增强暴击",
  "降低暴击",
  "增强穿刺",
  "降低穿刺",
  "增强命中",
  "降低命中",
  "持续回血",
  "持续伤害",
  "无敌",
  "复活",
  "眩晕",
  "麻痹",
  "狂暴",
  "提高血量上限",
  "随机增强属性",
  "随机削弱属性",
  "多属性并存",
  "沉默",
  "加速",
  "减速"
];
const BuffTrigger = [
  "出生自带",
  "技能释放",
  "攻击命中",
  "闪避成功",
  "受击后",
  "死亡后",
  "有前置Buff",
  "目标血量低于百分比",
  "技能命中",
  "直接触发",
];
export default {
  data() {
    return {
      formKey: "buff",
      curItem: "",
      itemData: {},
      typeStr: BuffType[0],
      buffType: BuffType,
      triggerStr: BuffTrigger[0],
      bufftrigger: BuffTrigger,
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      this.itemData = data;
      console.log("getItemData", data);
      if (this.itemData) {
        if (!this.itemData.type) this.itemData.type = 0;
        if (!this.itemData.trigger) this.itemData.trigger = 0;
        if (!this.itemData.time) this.itemData.time = -1;
        if (!this.itemData.probability) this.itemData.probability = 1;
        if (!this.itemData.value) this.itemData.value = 0;
        // if(!this.itemData.isSceneShow) this.itemData.isSceneShow=false;
        if (!this.itemData.isSceneShow)
          Vue.set(this.itemData, "isSceneShow", false);
        this.typeStr = BuffType[this.itemData.type];
        this.triggerStr = BuffTrigger[this.itemData.trigger];
      } else {
        this.itemData = {};
      }
    },
    selectTypeBox: function (command) {
      this.itemData.type = parseInt(command);
      this.typeStr = BuffType[command];
    },
    selectTriggerBox: function (command) {
      this.itemData.trigger = parseInt(command);
      this.triggerStr = BuffTrigger[command];
    },
  },
};
</script>