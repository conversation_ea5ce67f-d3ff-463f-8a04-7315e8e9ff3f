<style>
.levelcontainer {
  list-style: none;
  float: left;
  text-align: left;
  padding-left: 0;
  width: 100%;
  vertical-align: middle;
}
.levelcontainer li {
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 5px 0;
}
.itemname {
  width: 150px;
  float: left;
}
.itemvalue {
  float: left;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.enemyK {
  width: 200px;
  float: left;
  height: 40px;
  overflow: auto;
}
.enemyV {
  width: 100px;
  float: left;
}
.enemyLine {
  width: 100%;
  height: 40px;
  line-height: 40px;
}
</style>
<template>
    <mylistview :formKey="formKey" @setItemData="getItemData">
        <ul class="levelcontainer">
            <li v-for="(value, key) in crystalExploreConfig" :key="key">
                <span class="enemyK">
                    {{ value.name }}
                </span>
                <div class="enemyLine" v-if="(value.type=='string')">
                    <el-input
                    class="enemyV"
                    v-model="itemData[value.config]"
                    style="width: 50%"
                    ></el-input>
                </div>
                <div class="enemyLine" v-else>
                    <el-input
                        v-model.number="itemData[value.config]"
                        type="number"
                        class="enemyV"
                        style="width: 50%"
                    >
                    </el-input>
                </div>
            </li>
        </ul>
    </mylistview>
</template>
<script>
import mylistview from "./MyListView.vue";
import Vue from "vue";
const boolType=["是","否"];
export default {
  data() {
    return {
      formKey: "crystalExplore",
      curItem: "",
      itemData: {},
      crystalExploreConfig: [
          { name: "关卡名称", config: "levelName",type:"string"},
          { name: "金币花费", config: "goldIconSpend"},
          { name: "钻石花费", config: "diamondSpend"},
          { name: "前进概率", config: "forwardProbability"},
          { name: "后退概率", config: "backProbability"},
          { name: "蓝色掉落概率", config: "blueFallingProbability"},
          { name: "紫色掉落概率", config: "violetFallingProbability"},
          { name: "红色掉落概率", config: "redFallingProbability"},
          { name: "橙色掉落概率", config: "orangeFallingProbability"},
      ],
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      this.itemData = data;
    },
  },
};
</script>