{"name": "atmeditor", "version": "0.0.2", "author": "", "description": "奥特曼传奇英雄编辑器", "license": null, "main": "./dist/electron/main.js", "scripts": {"build": "node .electron-vue/build.js && electron-builder", "build:dir": "node .electron-vue/build.js && electron-builder --dir", "build:clean": "cross-env BUILD_TARGET=clean node .electron-vue/build.js", "build:web": "cross-env BUILD_TARGET=web node .electron-vue/build.js", "dev": "node .electron-vue/dev-runner.js", "pack": "npm run pack:main && npm run pack:renderer", "pack:main": "cross-env NODE_ENV=production webpack --progress --colors --config .electron-vue/webpack.main.config.js", "pack:renderer": "cross-env NODE_ENV=production webpack --progress --colors --config .electron-vue/webpack.renderer.config.js", "postinstall": ""}, "build": {"productName": "atmeditor", "appId": "com.elevue.atmeditor", "artifactName": "atmeditor Setup.${ext}", "directories": {"output": "build"}, "files": ["dist/electron/**/*"], "dmg": {"contents": [{"x": 410, "y": 150, "type": "link", "path": "/Applications"}, {"x": 130, "y": 150, "type": "file"}]}, "mac": {"icon": "build/icons/icon.icns"}, "win": {"icon": "build/icons/icon.ico"}, "linux": {"icon": "build/icons"}}, "dependencies": {"vue": "^2.5.16", "axios": "^0.18.0", "vue-electron": "^1.0.6", "vue-router": "^3.0.1", "vuex": "^3.0.1", "vuex-electron": "^1.0.0"}, "devDependencies": {"ajv": "^6.5.0", "babel-core": "^6.26.3", "babel-loader": "^7.1.4", "babel-minify-webpack-plugin": "^0.3.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "babel-preset-env": "^1.7.0", "babel-preset-stage-0": "^6.24.1", "babel-register": "^6.26.0", "cfonts": "^2.1.2", "chalk": "^2.4.1", "copy-webpack-plugin": "^4.5.1", "cross-env": "^5.1.6", "css-loader": "^0.28.11", "del": "^3.0.0", "devtron": "^1.4.0", "electron": "^2.0.4", "electron-builder": "^20.19.2", "electron-debug": "^1.5.0", "electron-devtools-installer": "^2.2.4", "element-ui": "^2.15.3", "file-loader": "^1.1.11", "html-webpack-plugin": "^3.2.0", "listr": "^0.14.3", "mini-css-extract-plugin": "0.4.0", "multispinner": "^0.2.1", "node-loader": "^0.6.0", "style-loader": "^0.21.0", "url-loader": "^1.0.1", "vue-html-loader": "^1.2.4", "vue-loader": "^15.2.4", "vue-style-loader": "^4.1.0", "vue-template-compiler": "^2.5.16", "webpack": "^4.15.1", "webpack-cli": "^3.0.8", "webpack-dev-server": "^3.1.4", "webpack-hot-middleware": "^2.22.2", "webpack-merge": "^4.1.3"}}