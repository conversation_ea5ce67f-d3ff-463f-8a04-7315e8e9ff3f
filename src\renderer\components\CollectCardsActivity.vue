<style>
.activitycontainer {
    list-style: none;
    padding-left: 0;
    width: 100%;
    overflow: hidden;
}

.activitycontainer li {
    width: 100%;
    min-height: 40px;
    margin: 5px 0;
    clear: both;
}

.itemname {
    width: 150px;
    float: left;
    line-height: 40px;
}

.itemvalue {
    width: 50%;
    float: left;
}

/* 礼包项样式 */
.gift-item {
    border: 1px solid #eee;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    width: 100%;
}

/* 表单项样式 */
.form-row {
    display: flex;
    align-items: center;
    margin: 10px 0;
}

.form-label {
    flex-shrink: 0;
    text-align: right;
    white-space: nowrap;
}

/* 奖励项样式 */
.reward-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 5px 0;
}

/* 操作按钮样式 */
.operation-btn {
    margin-left: 10px;
    cursor: pointer;
}

.card-requirements {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin: 5px 0;
}

.requirement-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-pool-item {
    border: 1px solid #eee;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 10px;
    white-space: nowrap;
}

.exchange-reward-item {
    border: 1px solid #eee;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
}

.exchange-reward-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 5px 0;
    white-space: nowrap;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    padding: 5px;
    background: #f5f7fa;
    border-radius: 4px;
    margin: 5px 0;
}

.section-content {
    transition: all 0.3s;
}
</style>

<template>
    <mylistview :formKey="formKey" @setItemData="getItemData">
        <div class="activitycontainer">
            <!-- 基本信息 -->
            <div class="section-header" @click="toggleSection('basic')">
                <h3 style="margin: 0;">基本信息</h3>
                <i :class="['el-icon-arrow-down', { 'is-active': !sections.basic }]"></i>
            </div>
            <div class="section-content" v-show="sections.basic">
                <div class="form-row">
                    <div class="itemname">名称:</div>
                    <el-input class="itemvalue" v-model="itemData.name" placeholder="请输入活动名称"></el-input>
                </div>

                <div class="form-row">
                    <div class="itemname">描述:</div>
                    <el-input class="itemvalue" type="textarea" v-model="itemData.description"
                        placeholder="请输入活动描述"></el-input>
                </div>
            </div>

            <!-- 掉落概率设置 -->
            <div class="section-header" @click="toggleSection('dropRate')">
                <h3 style="margin: 0;">掉落概率设置(掉落后概率重置)</h3>
                <i :class="['el-icon-arrow-down', { 'is-active': !sections.dropRate }]"></i>
            </div>
            <div class="section-content" v-show="sections.dropRate">
                <div class="form-row">
                    <i class="el-icon-plus operation-btn" @click="addDropRate"></i>
                </div>
                <div v-for="(rate, index) in itemData.dropRates" :key="index" class="card-pool-item">
                    <span class="form-label" style="width: auto;">第{{index + 1}}次游玩概率(1-100)%:</span>
                    <el-input type="number" v-model.number="rate.probability" :min="1" :max="100" placeholder="概率"
                        style="width: 100px"></el-input>
                    <i class="el-icon-delete operation-btn" @click="removeDropRate(index)"></i>
                </div>
            </div>

            <!-- 卡池配置 -->
            <div class="section-header" @click="toggleSection('cardPool')">
                <h3 style="margin: 0;">卡池配置</h3>
                <i :class="['el-icon-arrow-down', { 'is-active': !sections.cardPool }]"></i>
            </div>
            <div class="section-content" v-show="sections.cardPool">
                <div class="form-row">
                    <div class="itemname">每次获取勋章数:</div>
                    <el-input type="number" v-model.number="itemData.medalPerDraw" :min="1" placeholder="请输入每次获取的勋章数" style="width: 200px"></el-input>
                </div>
                <div class="form-row">
                    <i class="el-icon-plus operation-btn" @click="addCard"></i>
                </div>
                <div v-for="(card, index) in itemData.cardPool" :key="index" class="card-pool-item">
                    <span class="form-label" style="width: auto;">品质:</span>
                    <el-select v-model="card.quality" placeholder="品质" style="width: 100px">
                        <el-option label="绿色" :value="1"></el-option>
                        <el-option label="蓝色" :value="2"></el-option>
                        <el-option label="紫色" :value="3"></el-option>
                        <el-option label="金色" :value="4"></el-option>
                        <el-option label="红色" :value="5"></el-option>
                    </el-select>
                    <span class="form-label" style="width: auto;">卡牌名称:</span>
                    <el-input v-model="card.name" placeholder="卡牌名称" style="width: 150px"></el-input>
                    <span class="form-label" style="width: auto;">概率(1-100)%:</span>
                    <el-input type="number" v-model.number="card.probability" :min="1" :max="100" placeholder="概率"
                        style="width: 100px"></el-input>
                    <span class="form-label" style="width: auto;">卡牌图标:</span>
                    <el-input v-model="card.icon" placeholder="卡牌图标" style="width: 150px"></el-input>
                    <i class="el-icon-delete operation-btn" @click="removeCard(index)"></i>
                </div>
            </div>

            <!-- 兑换奖励池 -->
            <div class="section-header" @click="toggleSection('exchangePool')">
                <h3 style="margin: 0;">兑换奖励池</h3>
                <i :class="['el-icon-arrow-down', { 'is-active': !sections.exchangePool }]"></i>
            </div>
            <div class="section-content" v-show="sections.exchangePool">
                <div class="form-row">
                    <i class="el-icon-plus operation-btn" @click="addExchangeReward"></i>
                </div>
                <div v-for="(reward, index) in itemData.exchangeCardPool" :key="index" class="exchange-reward-item">
                    <div class="exchange-reward-header">
                        <div style="display: flex; flex-direction: column; gap: 5px; width: 100%;">
                            <i class="el-icon-delete operation-btn" style="align-items: start;"
                                @click="removeExchangeReward(index)"></i>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span class="form-label" style="width: auto;">奖励ID:</span>
                                <el-select v-model="reward.rewardId" clearable filterable allow-create
                                    placeholder="请选择奖励" class="product-input" style="width: 200px;">
                                    <el-option v-for="(item, idx) in CommonlyArticles" :key="idx"
                                        :label="getArticleName(item)" :value="item"></el-option>
                                </el-select>
                                <span v-if="reward.rewardId">{{getArticleName(reward.rewardId)}}</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span class="form-label" style="width: auto;">奖励数量:</span>
                                <el-input type="number" v-model.number="reward.rewardCount" :min="1" placeholder="奖励数量"
                                    style="width: 100px"></el-input>
                                <span class="form-label" style="width: auto;">可兑换次数(0表示无限):</span>
                                <el-input type="number" v-model.number="reward.exchangeCount" :min="1"
                                    placeholder="可兑换次数" style="width: 100px"></el-input>
                            </div>
                        </div>
                    </div>
                    <div class="card-requirements">
                        <div class="form-label">兑换要求:</div>
                        <div v-for="(req, reqIndex) in reward.requirements" :key="reqIndex" class="requirement-item">
                            <span class="form-label" style="width: auto;">卡牌:</span>
                            <el-select v-model="req.cardId" placeholder="选择卡牌" style="width: 150px">
                                <el-option v-for="card in itemData.cardPool" :key="card.name" :label="card.name"
                                    :value="card.name"></el-option>
                            </el-select>
                            <span class="form-label" style="width: auto;">数量:</span>
                            <el-input type="number" v-model.number="req.count" :min="1" placeholder="数量"
                                style="width: 100px"></el-input>
                            <i class="el-icon-delete operation-btn" @click="removeRequirement(index, reqIndex)"></i>
                        </div>
                        <i class="el-icon-plus operation-btn" style="align-self: start;"
                            @click="addRequirement(index)"></i>
                    </div>
                </div>
            </div>
        </div>
    </mylistview>
</template>

<script>
import Vue from "vue";
import mylistview from "./MyListView.vue";
export default {
    data() {
        return {
            formKey: "collectCards",
            CommonlyArticles: [],
            sections: {
                basic: true,
                dropRate: true,
                cardPool: true,
                exchangePool: true
            },
            itemData: {
                name: "",
                description: "",
                dropRates: [],
                cardPool: [],
                exchangeCardPool: [],
                medalPerDraw: 1
            }
        };
    },
    components: {
        mylistview,
    },
    methods: {
        toggleSection(section) {
            this.sections[section] = !this.sections[section];
        },
        getItemData(data) {
            if (data) {
                // 确保每个兑换奖励都有 requirements 数组
                if (data.exchangeCardPool) {
                    data.exchangeCardPool.forEach(reward => {
                        if (!reward.requirements) {
                            Vue.set(reward, 'requirements', []);
                        }
                        // 确保每个要求都有正确的数据结构
                        reward.requirements.forEach(req => {
                            if (typeof req.count !== 'number') {
                                Vue.set(req, 'count', 1);
                            }
                        });
                    });
                }
                // 确保dropRates数组存在
                if (!data.dropRates) {
                    Vue.set(data, 'dropRates', []);
                }
                this.itemData = data;
            } else {
                this.itemData = {
                    name: "",
                    description: "",
                    dropRates: [],
                    cardPool: [],
                    exchangeCardPool: [],
                    medalPerDraw: 1
                };
            }
            this.CommonlyArticles = global.gamedata["CommonlyArticles"] || [];
        },
        addCard() {
            if (!this.itemData.cardPool) {
                Vue.set(this.itemData, 'cardPool', []);
            }
            this.itemData.cardPool.push({
                quality: 1,
                name: "",
                probability: 1,
                icon: ""
            });
        },
        removeCard(index) {
            this.itemData.cardPool.splice(index, 1);
        },
        addExchangeReward() {
            if (!this.itemData.exchangeCardPool) {
                Vue.set(this.itemData, 'exchangeCardPool', []);
            }
            const newReward = {
                rewardId: "",
                rewardCount: 1,
                exchangeCount: 1,
                requirements: []
            };
            this.itemData.exchangeCardPool.push(newReward);
        },
        removeExchangeReward(index) {
            this.itemData.exchangeCardPool.splice(index, 1);
        },
        addRequirement(rewardIndex) {
            if (!this.itemData.exchangeCardPool[rewardIndex].requirements) {
                Vue.set(this.itemData.exchangeCardPool[rewardIndex], 'requirements', []);
            }
            const newRequirement = {
                cardId: "",
                count: 1
            };
            this.itemData.exchangeCardPool[rewardIndex].requirements.push(newRequirement);
        },
        removeRequirement(rewardIndex, reqIndex) {
            this.itemData.exchangeCardPool[rewardIndex].requirements.splice(reqIndex, 1);
        },
        getArticleName(articleId) {
            let _article = global.gamedata["article"][articleId];
            if (!_article) return "";
            return _article.name;
        },
        addDropRate() {
            if (!this.itemData.dropRates) {
                Vue.set(this.itemData, 'dropRates', []);
            }
            this.itemData.dropRates.push({
                probability: 1
            });
        },
        removeDropRate(index) {
            this.itemData.dropRates.splice(index, 1);
        },
    },
};
</script>