<template>
  <mylistview :formKey="formKey" v-on:setItemData="getItemData">
    <ul class="levelcontainer">
      <li>
        <div class="enemyLine">
          <span class="enemyK">名字:</span>
          <el-input
            class="enemyV"
            v-model="itemData.name"
            placeholder="请输入源晶名称"
            style="width: 50%"
          ></el-input>
        </div>
      </li>
      <li>
        <div class="itemname">类型:</div>
        <el-dropdown class="itemvalue" @command="selectTypeBox">
          <span class="el-dropdown-link">
            {{ typeStr }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(value, idx) in typeType" :command="idx" :key="idx">{{
              value
            }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </li>
      <li v-for="(value, key) in dataconfigs" :key="key">
        <div class="enemyLine">
          <span class="enemyK">{{ value.name }}</span>
          <el-input
            v-if="value.type === 'string'"
            class="enemyV"
            v-model="itemData[value.config]"
            style="width: 50%"
          ></el-input>
          <el-input-number
            v-else
            class="enemyV"
            type="number"
            v-model.number="itemData[value.config]"
            style="width: 50%"
          ></el-input-number>
        </div>
      </li>
    </ul>
  </mylistview>
</template>
<script>
import mylistview from "./MyListView.vue";
const typeType = [
  "生命值",
  "攻击力",
  "防御",
  "闪避强化",
  "暴击强化",
  "穿刺加成",
  "冷却缩减",
];
export default {
  data() {
    return {
      formKey: "crystal",
      curItem: "",
      itemData: {},
      typeStr: "",
      typeType: typeType,
      dataconfigs: [
        { name: "基础数值", config: "base" },
        { name: "等级数值增量", config: "add" },
        { name: "品质基础等级数值", config: "qualitybase" },
        { name: "品质数值等级增量", config: "addquality" },
      ],
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      this.itemData = data;
      console.log("getItemData", data);
      if (this.itemData) {
        this.typeStr = typeType[this.itemData.type] || "";
      } else {
        this.itemData = {};
      }
    },
    selectTypeBox: function (command) {
      this.itemData.type = parseInt(command);
      this.typeStr = typeType[command];
    },
  },
};
</script>