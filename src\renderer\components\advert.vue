<template>
  <mylistview :formKey="formKey" v-on:setItemData="getItemData">
    <ul class="levelcontainer">
      <li>
        <div class="itemname" style="width: 500px">广告埋点(位置+id):</div>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addAdvert"
        ></i>
        <el-collapse>
          <el-collapse-item v-for="(v1, k1) in itemData.advs" :key="k1">
            <template class="itemname" slot="title">
              位置：
              <el-input
                v-model="v1.pos"
                placeholder="请输入广告位"
                style="width: 30%"
              ></el-input>
              <el-input
                v-model="v1.id"
                placeholder="请输入广告id"
                style="width: 30%"
              ></el-input>
              <i
                class="el-icon-delete"
                style="font-size: 25px; vertical-align: middle"
                @click="removeAdvert(k1)"
              ></i>
            </template>
          </el-collapse-item>
        </el-collapse>
      </li>
    </ul>
  </mylistview>
</template>
<script>
import mylistview from "./MyListView.vue";
import Vue from "vue";
export default {
  data() {
    return {
      formKey: "advert",
      itemData: {},
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      this.itemData = data;
      console.log("getItemData", data);
      if (!this.itemData) {
        this.itemData = { advs: [] };
      }
    },
    addAdvert: function () {
      let item = { pos: "", id: "" };
      if (!this.itemData.advs || this.itemData.advs.length == 0) {
        Vue.set(this.itemData, "advs", [item]);
      } else {
        this.itemData.advs.push(item);
      }
    },
    removeAdvert: function (idx) {
      var adv = this.itemData.advs[idx];
      this.$confirm("是否删除广告位 " + adv.pos + " 的配置?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.itemData.advs.splice(idx, 1);
      });
    },
  },
};
</script>