<template>
  <el-container :formKey="formKey">
    <ul class="levelcontainer">
      <li>
        <div class="enemyLine" style="float: left">
          <span class="enemyK">玩家攻击系数</span>
          <el-input-number
            class="enemyV"
            v-model.number="itemData.roleAttackCoefficient"
            style="width: 50%"
          ></el-input-number>
        </div>
      </li>
      <li style="height: auto">
        <el-collapse>
          <el-collapse-item>
            <template class="itemname" slot="title">
              主界面显示角色
              <i
                class="el-icon-plus"
                style="font-size: 25px; vertical-align: middle"
                @click="addMainRole(itemData.mainSceneRoleIDs)"
              ></i>
            </template>
            <ul>
              <li
                v-for="(v1, k1) in itemData.mainSceneRoleIDs"
                :key="k1"
                class="itemname"
                slot="title"
                style="height: auto"
              >
                <span class="enemyK">角色{{k1+1}} ID：</span>
                <el-input
                  v-model="itemData.mainSceneRoleIDs[k1]"
                  placeholder="请输入角色ID"
                  style="width: 50%"
                ></el-input>
                <i
                  class="el-icon-delete"
                  style="font-size: 25px; vertical-align: middle"
                  @click="removeMainRole(itemData.mainSceneRoleIDs, k1)"
                ></i>
              </li>
            </ul>
          </el-collapse-item>
        </el-collapse>
      </li>
      <li v-for="(value, key) in upgradeconfigs" :key="key">
        <div class="" v-if="value.length">
          <span class="enemyLine" style="float: left">{{ value.name }}</span>
          <div
            class="enemyLine"
            style="float: left"
            v-for="idx in value.length"
            :key="idx"
          >
            <span class="enemyK">{{ idx }}</span>
            <el-input
              v-if="value.type === 'string'"
              class="enemyV"
              v-model="itemData[value.config][idx - 1]"
              style="width: 50%"
            ></el-input>

            <el-input-number
              v-else
              class="enemyV"
              v-model.number="itemData[value.config][idx - 1]"
              style="width: 50%"
            ></el-input-number>
          </div>
        </div>
        <div class="enemyLine" style="float: left" v-else>
          <span class="enemyK">{{ value.name }}</span>
          <el-input
            v-if="value.type === 'string'"
            class="enemyV"
            v-model="itemData[value.config]"
            style="width: 50%"
          ></el-input>

          <el-input-number
            v-else
            class="enemyV"
            v-model.number="itemData[value.config]"
            style="width: 50%"
          ></el-input-number>
        </div>
      </li>
      <li>
        <div class="itemname" style="width: 400px">
          装备升级需要的道具:(id，起始数量，起始等级，增量)
        </div>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addEquipArticle"
        ></i>
        <el-collapse>
          <el-collapse-item
            v-for="(v1, k1) in itemData.equipArticles"
            :key="k1"
          >
            <template class="itemname" slot="title">
              道具：
              <!-- <i class="el-icon-more"></i> -->
              <el-input
                v-model="v1.id"
                placeholder="请输入道具id"
                style="width: 20%"
              ></el-input>
              <el-input
                type="number"
                v-model.number="v1.num"
                placeholder="请输入道具起始需求数量"
                style="width: 20%"
              ></el-input>
              <el-input
                type="number"
                v-model.number="v1.start"
                placeholder="请输入起始需求道具的等级"
                style="width: 20%"
              ></el-input>
              <el-input
                type="number"
                v-model.number="v1.addnum"
                placeholder="请输入道具需求数量的增量"
                style="width: 20%"
              ></el-input>
              <i
                class="el-icon-delete"
                style="font-size: 25px; vertical-align: middle"
                @click="removeEquipArticle(k1)"
              ></i>
            </template>
          </el-collapse-item>
        </el-collapse>
      </li>
    </ul>
  </el-container>
</template>
<script>
import Vue from "vue";
import globaldata from "../GlobalData.js";
export default {
  data() {
    return {
      formKey: "upgrade",
      curItem: "",
      itemData: {},
      upgradeconfigs: [
        { name: "角色升级经验初始：", config: "roleexp" },
        { name: "角色升级经验增量：", config: "roleaddexp" },
        { name: "角色进阶属性加成：", config: "advroleprops" },
        {
          name: "角色进阶等级上限：",
          config: "advrolemaxlevel",
          type: "string",
        },
        { name: "角色进阶金币：", config: "advrolegolds", length: 5 },
        { name: "角色进阶碎片：", config: "advroledebris", length: 5 },
        { name: "技能进阶经验：", config: "advSkillExp", length: 6 },
        { name: "源晶基础经验", config: "crystalexp" },
        { name: "源晶等级经验增量", config: "addcrystallevelexp" },
        { name: "源晶品质基础经验", config: "crystalqualityexp" },
        { name: "源晶品质经验增量", config: "addcrystalqualityexp" },
        { name: "装备升级需要的金币", config: "equipgold" },
        { name: "装备升级需要的金币增量", config: "addequipgold" },
        { name: "装备升级需要的钻石", config: "equipdiamond" },
        { name: "装备升级需要的钻石增量", config: "addequipdiamond" },
        { name: "装备等级上限", config: "equipmaxlevel" },
      ],
    };
  },
  components: {},
  mounted: function () {
    this.itemData = global.gamedata[this.formKey];
    console.log(this.itemData);
    if (!this.itemData) {
      var v = {};
      for (let k in this.upgradeconfigs) {
        if (this.upgradeconfigs[k]["length"]) {
          let n = [];
          for (let i = 0; i < this.upgradeconfigs[k]["length"]; i++) {
            n.push(0);
          }
          v[this.upgradeconfigs[k]["config"]] = n;
        } else {
          v[this.upgradeconfigs[k]["config"]] = 0;
        }
      }
      Vue.set(global.gamedata, this.formKey, v);
      this.itemData = global.gamedata[this.formKey];
    } else if (!this.itemData.advSkillExp) {
      Vue.set(this.itemData, "advSkillExp", [0, 0, 0, 0, 0, 0]);
    } else if (!this.itemData.roleAttackCoefficient) {
      Vue.set(this.itemData, "roleAttackCoefficient", 1);
    }
    if (!this.itemData.mainSceneRoleIDs) {
      Vue.set(this.itemData, "mainSceneRoleIDs", []);
    }
  },
  methods: {
    addEquipArticle: function () {
      var article = { id: "", num: 0, start: 0, addnum: 0 };
      if (
        !this.itemData.equipArticles ||
        this.itemData.equipArticles.length == 0
      ) {
        Vue.set(this.itemData, "equipArticles", [article]);
      } else {
        this.itemData.equipArticles.push(article);
      }
    },
    removeEquipArticle: function (idx) {
      // console.log(idx, this.itemData.MapWaves[idx]);
      var article = this.itemData.equipArticles[idx];
      this.$confirm("是否删除道具 " + article.id + " 的配置?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.itemData.equipArticles.splice(idx, 1);
      });
    },
    addMainRole: function (mainSceneRoleIDs) {
      mainSceneRoleIDs.push("");
    },
    removeMainRole: function (mainSceneRoleIDs, index) {
      mainSceneRoleIDs.splice(index, 1);
    },
  },
};
</script>