<style>
.activitycontainer {
    list-style: none;
    padding-left: 0;
    width: 100%;
    overflow: hidden;
}

.activitycontainer li {
    width: 100%;
    min-height: 40px;
    margin: 5px 0;
    clear: both;
}

.itemname {
    width: 150px;
    float: left;
    line-height: 40px;
}

.itemvalue {
    width: 50%;
    float: left;
}

/* 礼包项样式 */
.gift-item {
    border: 1px solid #eee;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    width: 100%;
}

/* 表单项样式 */
.form-row {
    display: flex;
    align-items: center;
    margin: 10px 0;
}

.form-label {
    width: 80px;
    flex-shrink: 0;
}

/* 奖励项样式 */
.reward-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 5px 0;
}

/* 操作按钮样式 */
.operation-btn {
    margin-left: 10px;
    cursor: pointer;
}
</style>

<template>
    <mylistview :formKey="formKey" @setItemData="getItemData">
        <div class="activitycontainer">
            <!-- 基本信息 -->
            <div class="form-row">
                <div class="itemname">名称:</div>
                <el-input class="itemvalue" v-model="itemData.name" placeholder="请输入活动礼包名称"></el-input>
            </div>

            <div class="form-row">
                <div class="itemname">描述:</div>
                <el-input class="itemvalue" type="textarea" v-model="itemData.description"
                    placeholder="请输入活动礼包描述"></el-input>
            </div>

            <!-- 礼包列表 -->
            <div class="form-row">
                <div class="itemname">礼包内容</div>
            </div>

            <!-- 礼包项列表 -->
            <div v-for="(gift, index) in itemData.gifts" :key="index" class="gift-item">
                <div class="form-row">
                    <h3 style="margin: 0;">第{{ index + 1 }}档礼包</h3>
                    <i class="el-icon-delete operation-btn" @click="removeGift(index)"></i>
                </div>

                <div class="form-row">
                    <div class="form-label">购买类型：</div>
                    <el-select v-model="gift.purchaseType" style="width: 200px"
                        @change="handlePurchaseTypeChange($event, index)">
                        <el-option v-for="type in purchaseTypes" :key="type.value" :label="type.label"
                            :value="type.value">
                        </el-option>
                    </el-select>
                </div>

                <div class="form-row" v-if="gift.purchaseType === PurchaseType.PAID">
                    <div class="form-label">计费ID：</div>
                    <el-input v-model="gift.payID" placeholder="请输入计费ID" style="width: 200px">
                    </el-input>
                </div>

                <div class="form-row">
                    <div class="form-label">奖励内容：</div>
                    <i class="el-icon-plus operation-btn" @click="addReward(index)"></i>
                </div>

                <div v-for="(reward, rIndex) in gift.rewards" :key="rIndex" class="reward-item">
                    <el-select v-model="reward.id" clearable filterable allow-create placeholder="奖励ID"
                        class="product-input" style="width: 200px;">
                        <el-option v-for="(item, idx) in CommonlyArticles" :key="idx" :label="getArticleName(item)"
                            :value="item" />
                    </el-select>
                    <el-input type="number" v-model.number="reward.count" placeholder="数量" style="width: 100px">
                    </el-input>
                    <i class="el-icon-delete operation-btn" @click="removeReward(index, rIndex)"></i>
                    <span>{{getArticleName(reward.id)}}</span>
                </div>
            </div>
        </div>
    </mylistview>
</template>

<script>
import Vue from "vue";
import mylistview from "./MyListView.vue";

// 添加购买类型枚举
const PurchaseType = {
    FREE: 0,    // 免费
    AD: 1,      // 广告
    PAID: 2     // 计费
};

export default {
    data() {
        return {
            formKey: "chainedGift",
            CommonlyArticles: [],
            itemData: {
                name: "",
                description: "",
                gifts: [
                    {
                        purchaseType: PurchaseType.FREE,
                        rewards: []
                    },
                    // ... 共6个礼包
                ]
            },
            PurchaseType: PurchaseType, // 添加到 data 中使模板可以访问
            purchaseTypes: [
                { label: '免费', value: PurchaseType.FREE },
                { label: '广告', value: PurchaseType.AD },
                { label: '计费', value: PurchaseType.PAID }
            ]
        };
    },
    components: {
        mylistview,
    },
    methods: {
        getItemData(data) {
            if (data) {
                // 删除可能存在的price字段
                if (data.gifts) {
                    data.gifts.forEach(gift => {
                        if (gift.price) delete gift.price;
                    });
                }
                this.itemData = data;
            } else {
                this.itemData = {
                    name: "",
                    description: "",
                    gifts: Array(6).fill(null).map(() => ({
                        purchaseType: PurchaseType.FREE,
                        payID: "",
                        rewards: []
                    }))
                };
            }
            this.CommonlyArticles = global.gamedata["CommonlyArticles"] || [];
            if (!this.itemData.gifts) {
                Vue.set(this.itemData, "gifts", Array(6).fill(null).map(() => ({
                    purchaseType: PurchaseType.FREE,
                    payID: "",
                    rewards: []
                })));
            }
        },
        addGift() {
            // 移除添加礼包功能
        },
        removeGift(index) {
            // 重置该礼包而不是删除
            Vue.set(this.itemData.gifts, index, {
                purchaseType: PurchaseType.FREE,
                rewards: []
            });
        },
        addReward(giftIndex) {
            const reward = {
                id: "",
                count: 1
            };
            if (!this.itemData.gifts[giftIndex].rewards) {
                Vue.set(this.itemData.gifts[giftIndex], "rewards", [reward]);
            } else {
                this.itemData.gifts[giftIndex].rewards.push(reward);
            }
        },
        removeReward(giftIndex, rewardIndex) {
            this.itemData.gifts[giftIndex].rewards.splice(rewardIndex, 1);
        },
        handlePurchaseTypeChange(type, index) {
            if (type === PurchaseType.PAID && !this.itemData.gifts[index].payID) {
                Vue.set(this.itemData.gifts[index], 'payID', '');
            }
        },
        handleRewardInput(value, reward) {
            if (value && !this.CommonlyArticles.includes(value)) {
                reward.id = value;
            }
        },
        getArticleName(articleId) {
            let _article = global.gamedata["article"][articleId];
            if (!_article) return "";
            return _article.name;
        }
    },
};
</script>