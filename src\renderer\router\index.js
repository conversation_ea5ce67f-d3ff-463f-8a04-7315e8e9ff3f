import achievement from '@/components/achievement'
import advert from '@/components/advert'
import article from '@/components/article'
import battlePass from '@/components/battlePass'
import buff from '@/components/buff'
import ChainedGift from '@/components/ChainedGift'
import CollectCardsActivity from '@/components/CollectCardsActivity'
import commonFalling from '@/components/commonFalling'
import crystal from '@/components/crystal'
import crystalExplore from '@/components/crystalExplore'
import equip from '@/components/equip'
import guide from '@/components/guide'
import HolidayActivity from '@/components/HolidayActivity'
import HomePage from '@/components/HomePage'
import level from '@/components/level'
import levelDetails from '@/components/levelDetails'
import NewHeroActivity from '@/components/NewHeroActivity'
import ouBuLevel from '@/components/ouBuLevel'
import plot from '@/components/plot'
import PropsPool from '@/components/PropsPool'
import Rank from '@/components/Rank'
import role from '@/components/role'
import roleSkin from '@/components/roleSkin'
import shop from '@/components/shop'
import specialLevel from '@/components/specialLevel'
import UltramanBattle from '@/components/UltramanBattle'
import upgrade from '@/components/upgrade'
import vipPrivilege from '@/components/vipPrivilege'
import WorldBoss from '@/components/WorldBoss'
import Vue from 'vue'
import VueRouter from 'vue-router'
Vue.use(VueRouter)

const routes = [{
    path: '',
    name: '首页',
    component: HomePage
}, {
    path: '/level',
    name: '关卡编辑',
    component: level
}, {
    path: '/levelDetails',
    name: '关卡详情编辑',
    component: levelDetails
}, {
    path: '/role',
    name: '角色编辑',
    component: role
}, {
    path: '/upgrade',
    name: '通用升级数据编辑',
    component: upgrade
}, {
    path: '/commonFalling',
    name: '通用掉落数据编辑',
    component: commonFalling
}, {
    path: '/equip',
    name: '装备编辑',
    component: equip
}, {
    path: '/article',
    name: '道具编辑',
    component: article
}, {
    path: '/crystal',
    name: '源晶编辑',
    component: crystal
}, {
    path: '/crystalExplore',
    name: '源晶探索编辑',
    component: crystalExplore
}, {
    path: '/vipPrivilege',
    name: 'vip特权编辑',
    component: vipPrivilege
}, {
    path: '/guide',
    name: '引导编辑',
    component: guide
}, {
    path: '/plot',
    name: '剧情编辑',
    component: plot
}, {
    path: '/buff',
    name: 'BUFF编辑',
    component: buff
}, {
    path: '/specialLevel',
    name: '特殊关卡通用数据编辑',
    component: specialLevel
}, {
    path: '/PropsPool',
    name: '道具池编辑',
    component: PropsPool
}, {
    path: '/Shop',
    name: '商城编辑',
    component: shop
}, {
    path: '/Achievement',
    name: '成就编辑',
    component: achievement
}, {
    path: '/Advert',
    name: '广告编辑',
    component: advert
},
{
    path: '/Rank',
    name: '排位编辑',
    component: Rank
}, {
    path: '/roleSkin',
    name: '角色皮肤编辑',
    component: roleSkin
}, {
    path: '/ouBuLevel',
    name: '欧布关卡编辑',
    component: ouBuLevel
}, {
    path: '/battlePass',
    name: '通行证编辑',
    component: battlePass
}, {
    path: '/NewHeroActivity',
    name: '新英雄活动礼包',
    component: NewHeroActivity
}, {
    path: '/ChainedGift',
    name: '链式活动礼包',
    component: ChainedGift
}, {
    path: '/HolidayActivity',
    name: '节日活动礼包',
    component: HolidayActivity
}, {
    path: '/CollectCardsActivity',
    name: '收集卡牌活动',
    component: CollectCardsActivity
}, {
    path: '/WorldBoss',
    name: '世界boss',
    component: WorldBoss
}, {
    path: '/UltramanBattle',
    name: '奥特大作战',
    component: UltramanBattle
}
];
const router = new VueRouter({
    routes
})
export default router