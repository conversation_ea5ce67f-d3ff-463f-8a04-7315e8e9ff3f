import { getData } from './yourModule';
import { expect } from 'chai';
import { saveDB } from "../src/db";
import { expect } from "chai";

describe("saveDB", () {
    it("should reject if no data file is read", (done) {
        const mockPromise = saveDB();
        mockPromise.then(() => {
            done("Expected promise to be rejected");
        }).catch((error) => {
            expect(error).to.equal("未读取数据文件");
            done();
        });
    });

    it("should save the data to the file", (done) {
        const mockPromise = saveDB();
        mockPromise.then(() => {
            done("Expected promise to be resolved");
        }).catch((error) => {
            done("Expected promise to be resolved");
        });
    });
});

describe('getData', () => {
    let originalGlobal;

    beforeEach(() => {
        originalGlobal = global.gamedata;
        global.gamedata = {
            testKey: {
                value: 'testValue'
            }
        };
    });

    afterEach(() => {
        global.gamedata = originalGlobal;
    });

    it('should return the data for the given key', () => {
        const result = getData('testKey');
        expect(result).to.deep.equal({ value: 'testValue' });
    });

    it('should return an empty object for unknown keys', () => {
        const result = getData('unknownKey');
        expect(result).to.deep.equal({});
    });
});