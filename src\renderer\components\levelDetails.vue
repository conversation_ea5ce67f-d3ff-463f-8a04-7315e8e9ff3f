<style>
.levelcontainer {
  list-style: none;
  float: left;
  text-align: left;
  padding-left: 0;
  width: 100%;
  vertical-align: middle;
}
.levelcontainer li {
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 5px 0;
}
.itemname {
  width: 150px;
  float: left;
}
.itemvalue {
  float: left;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.enemyK {
  width: 200px;
  float: left;
  height: 40px;
  overflow: auto;
}
.enemyV {
  width: 100px;
  float: left;
}
.enemyLine {
  width: 100%;
  height: 40px;
  line-height: 40px;
}
</style>
<template>
  <mylistview :formKey="formKey" @setItemData="getItemData">
    <ul class="levelcontainer">
      <li v-for="(value, key) in levelDetailsConfig" :key="key">
        <div class="enemyLine">
          <span class="enemyK">
            {{ value.name }}
          </span>
          <el-input
            v-if="value.type == 'string'"
            v-model="itemData[value.config]"
            class="enemyV"
            style="width: 50%"
          >
          </el-input>
          <el-input
            v-else
            v-model.number="itemData[value.config]"
            type="number"
            class="enemyV"
            style="width: 50%"
          >
          </el-input>
        </div>
      </li>
      <li>
        <div class="itemname">掉落物：</div>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addfallingGoods1"
        ></i>
        <ul>
          <li
            v-for="(v1, k1) in itemData.fallingGoods"
            :key="k1"
            class="itemname"
            slot="title"
            style="height:130px"
          >
            <span>掉落物{{ k1 + 1 }}：</span>
            <!-- <i class="el-icon-more"></i> -->
            <i
              class="el-icon-delete"
              style="font-size: 25px; vertical-align: middle"
              @click="removefallingGoods(k1)"
            ></i>
            <ul>
              <li>
                <span>掉落物ID：</span>
                <el-input
                  v-model="itemData.fallingGoods[k1].fallingGoodsID"
                  placeholder="请输入掉落物ID"
                  style="width: 50%"
                ></el-input>
              </li>
              <li>
                <span>掉落概率：</span>
                <el-input-number
                  v-model="itemData.fallingGoods[k1].fallingProbability"
                  placeholder="请输入掉落物ID"
                  style="width: 50%"
                ></el-input-number>
              </li>
            </ul>
          </li>
        </ul>
      </li>
    </ul>
  </mylistview>
</template>
<script>
import mylistview from "./MyListView.vue";
import Vue from "vue";
export default {
  data() {
    return {
      formKey: "levelDetails",
      curItem: "",
      itemData: {},
      levelDetailsConfig: [
        { name: "关卡名称", config: "levelName", type: "string" },
        { name: "建议等级", config: "suggsetLevel" },
        { name: "关卡描述", config: "levelDesc", type: "string" },
        { name: "掉落英雄名", config: "herofragmentID", type: "string" },
      ],
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      this.itemData = data;
      if(this.itemData.fallingGoodsIDs){
        Vue.set(this.itemData, "fallingGoods", []);
        for(let i=0;i<this.itemData.fallingGoodsIDs.length;i++){
          let fallgood={fallingGoodsID:"",fallingProbability:0}
          fallgood.fallingGoodsID=this.itemData.fallingGoodsIDs[i];
          this.itemData.fallingGoods.push(fallgood);
        }
        delete this.itemData.fallingGoodsIDs;
      }
    },
    addfallingGoods1: function () {
      let fallingGoods = {fallingGoodsID:"",fallingProbability:0}
      if (!this.itemData.fallingGoods ||this.itemData.fallingGoods.length == 0) {
        Vue.set(this.itemData, "fallingGoods", [fallingGoods]);
      } else {
        this.itemData.fallingGoods.push(fallingGoods);
      }
    },
    removefallingGoods: function (idx) {
      this.itemData.fallingGoods.splice(idx, 1);
    },
  },
};
</script>