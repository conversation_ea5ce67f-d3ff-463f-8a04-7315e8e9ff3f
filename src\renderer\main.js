import Vue from 'vue'
import axios from 'axios'

import App from './App'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import { remote } from 'electron'
import globaldata from './GlobalData'
const ipcRenderer = require('electron').ipcRenderer;

if (!process.env.IS_WEB) Vue.use(require('vue-electron'))
Vue.http = Vue.prototype.$http = axios
Vue.config.productionTip = false
Vue.use(ElementUI)

/* eslint-disable no-new */
new Vue({
    components: { App },
    router,
    store,
    template: '<App/>'
}).$mount('#app')

var Menu = remote.Menu;
var template = [{
    label: "文件",
    submenu: [{
            label: "新建",
            click: function() {
                globaldata.createDB();
            }
        }, {
            label: "打开",
            click: function() {
                globaldata.readDB();
            }
        },
        {
            label: "保存",
            accelerator: "CmdOrCtrl+S",
            click: function() {
                console.log("save---")
                globaldata.saveDB();
            }
        }, {
            label: "另存为",
            click: function() {
                console.log("save to---")
                globaldata.saveDBto();
            }
        }, {
            label: "导出数据",
            click: function() {
                console.log("export----");
                globaldata.exportAllData();
            }
        }, {
            label: "设置导出目录",
            click: function() {
                console.log("exportpath----");
                globaldata.setExportPath();
            }
        }, {
            label: "控制台",
            click: function() {
                console.log("send--------")
                ipcRenderer.send('openDevTools')
                    // mainWindow.webContents.openDevTools({ mode: 'right' })
            }
        }
        // {
        //     label: "退出",
        //     role: "quit"
        // }
    ]
}]
Menu.setApplicationMenu(Menu.buildFromTemplate(template));