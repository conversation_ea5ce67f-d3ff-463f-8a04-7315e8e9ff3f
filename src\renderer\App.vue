<template>
  <div id="app">
    <!-- <img src="./assets/logo.png" /> -->
    <div class="main-header">
      <span class="main-title">{{ headTitle }}</span>
      <el-button
        class="back-button"
        type="primary"
        v-if="showback"
        @click="returnHomePage"
        style="margin-left: 5px; margin-top: 5px"
        >返回</el-button
      >
    </div>
    <router-view
      style="
        height: calc(100% - 50px);
        width: 100%;
        position: absolute;
        top: 50px;
        padding-left: 0px;
      "
    />
  </div>
</template>

<script>
import globaldata from "./GlobalData.js";
// var globaldata = require("../GlobalData.js");
export default {
  name: "App",
  data() {
    return {
      showback: false,
      headTitle: "首页",
    };
  },
  methods: {
    returnHomePage: function () {
      console.log("return home page");
      this.$router.go(-1); //返回上一层
    },
  },
  mounted: function () {
    console.log("zzzzzzzz");
    console.log(globaldata);
    this.returnHomePage();
    // globaldata.readFile();
  },
  watch: {
    $route(now, old) {
      // console.log("11111111111")
      //监控路由变换，控制返回按钮的显示
      if (now.path == "/") {
        this.showback = false;
      } else {
        if (!globaldata.hasData()) {
          this.$message({
            type: "info",
            message: "还未读取配置数据",
          });

          this.returnHomePage();
        }
        this.showback = true;
      }
      this.headTitle = now.name;
    },
  },
};
</script>

<style>
body {
  margin: 0;
}
#app {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  width: 100%;
  height: 100%;
  position: absolute;
  /* margin-top: 60px; */
}
.main-header {
  height: 50px;
  line-height: 50px;
  background: #525252;
}
.main-title {
  color: white;
  font-size: 30px;
  position: absolute;
  width: 200px;
  left: 50%;
  margin-left: -100px;
}
.back-button {
  float: left;
  height: 40px;
}
</style>
