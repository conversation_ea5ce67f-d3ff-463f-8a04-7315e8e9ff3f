<template>
  <el-container style="border: 1px solid #eee:width:100%">
    <el-aside width="200px" style="background-color: rgb(238, 241, 246)">
      <el-header style="text-align: right; font-size: 12px">
        <i
          v-if="curItem != null && curItem != ''"
          class="el-icon-document"
          style="margin-right: 15px; font-size: 30px; vertical-align: middle"
          @click="copyListItem"
        ></i>
        <i
          class="el-icon-circle-plus-outline"
          style="margin-right: 15px; font-size: 30px; vertical-align: middle"
          @click="addListItem"
        ></i>
        <i
          class="el-icon-remove-outline"
          style="margin-right: 15px; font-size: 30px; vertical-align: middle"
          @click="removeListItem"
        ></i>
      </el-header>
      <ul
        style="
          list-style: none;
          float: left;
          font-size: 25px;
          width: 100%;
          padding-left: 0;
        "
      >
        <li
          class="listitem"
          v-for="(value, key) in listdata"
          :key="key"
          @click="selectItem(key)"
          v-bind:class="{ active: key == curItem }"
        >
          {{ key }}
        </li>
      </ul>
    </el-aside>

    <el-container v-if="itemData != null">
      <el-main :data="itemData"> <slot></slot> </el-main>
    </el-container>
  </el-container>
</template>
<style>
.el-header {
  background-color: #b3c0d1;
  color: #333;
  line-height: 60px;
}

.el-aside {
  color: #333;
}
.listitem {
  padding: 5px;
}
.active {
  background: #a1ff9f;
}
</style>

<script>
import Vue from "vue";
import globaldata from "../GlobalData.js";
Vue.component("mylistview", { props: ["formKey"] });
export default {
  data() {
    return {
      listdata: {},
      itemData: {},
      curItem: "",
    };
  },
  props: {
    formKey: String,
  },
  mounted: function () {
    this.listdata = globaldata.getData(this.formKey);
    this.itemData = null;
    this.curItem = "";
    this.$emit("curItem", this.curItem);
    this.$emit("setItemData", this.itemData);
  },
  methods: {
    selectItem: function (key) {
      console.log("selectItem", key);
      this.curItem = key;
      this.itemData = this.listdata[this.curItem] || null;
      this.$emit("curItem", this.curItem);
      this.$emit("setItemData", this.itemData);
    },
    addListItem: function () {
      console.log("addListItem");
      this.$prompt("请输入名称", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(({ value }) => {
          if (value && value != "") {
            if (this.listdata[value]) {
              this.$message({
                type: "info",
                message: "列表里已有同名项",
              });
            } else {
              this.$message({
                type: "success",
                message: "添加成功：" + value,
              });
              this.listdata[value] = { name: value };
              globaldata.setData(this.formKey, this.listdata);
              this.selectItem(value);
            }
          } else {
            this.$message({
              type: "info",
              message: "请输入要添加的名称",
            });
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消添加",
          });
        });
      // this.listdata["1-4"] = { name: "1-4" };
    },
    removeListItem: function () {
      // console.log(this.curItem, this.listdata[this.curItem]);
      if (
        !this.curItem ||
        this.curItem == "" ||
        this.listdata == null ||
        this.listdata[this.curItem] == null
      ) {
        this.$message({
          type: "info",
          message: "当前未选择要删除的选项",
        });
      } else {
        this.$confirm("此操作将删除当前选项, 是否继续?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.$message({
              type: "success",
              message: "删除成功!",
            });
            delete this.listdata[this.curItem];
            this.selectItem("");
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消删除",
            });
          });
      }
    },
    copyListItem: function () {
      console.log("copyListItem");
      let v = this;
      this.$prompt("请输入名称", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(({ value }) => {
          if (value && value != "") {
            if (v.listdata[value]) {
              v.$message({
                type: "info",
                message: "列表里已有同名项",
              });
            } else {
              v.$message({
                type: "success",
                message: "添加成功：" + value,
              });
              v.listdata[value] = JSON.parse(JSON.stringify(v.listdata[v.curItem]))
              v.listdata[value].name = value;
              globaldata.setData(v.formKey, v.listdata);
              v.selectItem(value);
            }
          } else {
            v.$message({
              type: "info",
              message: "请输入要添加的名称",
            });
          }
        })
        .catch(() => {
          v.$message({
            type: "info",
            message: "取消添加",
          });
        });
    },
  },
};
</script>