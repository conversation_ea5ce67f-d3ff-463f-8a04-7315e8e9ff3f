<template>
  <el-container :formKey="formKey">
    <ul class="levelcontainer">
      <li v-for="(value, key) in commonRankconfigs" :key="key">
        <div class="" v-if="value.length">
          <span class="enemyLine" style="float: left">{{ value.name }}</span>
          <div
            class="enemyLine"
            style="float: left"
            v-for="idx in value.length"
            :key="idx"
          >
            <span class="enemyK">{{ idx }}</span>
            <el-input
              v-if="value.type === 'string'"
              class="enemyV"
              v-model="itemData[value.config][idx - 1]"
              style="width: 50%"
            ></el-input>

            <el-input-number
              v-else
              class="enemyV"
              v-model.number="itemData[value.config][idx - 1]"
              style="width: 50%"
            ></el-input-number>
          </div>
        </div>
        <div class="enemyLine" style="float: left" v-else>
          <span class="enemyK">{{ value.name }}</span>
          <el-input
            v-if="value.type === 'string'"
            class="enemyV"
            v-model="itemData[value.config]"
            style="width: 50%"
          ></el-input>

          <el-input-number
            v-else
            class="enemyV"
            v-model.number="itemData[value.config]"
            style="width: 50%"
          ></el-input-number>
        </div>
      </li>
      <li style="height: auto">
        <div class="itemname">添加排位池：</div>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addRanksPool"
        ></i>
        <el-collapse>
          <el-collapse-item v-for="(v1, k1) in itemData.RankPools" :key="k1">
            <template class="itemname" slot="title">
              {{ k1 }}
              <i
                class="el-icon-delete"
                style="font-size: 25px; vertical-align: middle"
                @click="removeRanksPool(k1)"
              ></i>
            </template>
            <ul>
              <li class="itemname">
                <span>段位名称：</span>
                <el-input
                  v-model="v1.rankName"
                  style="width: 50%"
                ></el-input>
              </li>
              <li class="itemname" slot="title" style="height: auto">
                <span>战斗结算奖励：</span>
                <i
                  class="el-icon-plus"
                  style="font-size: 25px; vertical-align: middle"
                  @click="addRankEndReward(v1.rankAttackEndRewards)"
                ></i>
                <ul>
                  <li
                    v-for="(v2, k2) in v1.rankAttackEndRewards"
                    :key="k2"
                    class="itemname"
                    slot="title"
                    style="height: auto"
                  >
                    <span>掉落物{{ k2 + 1 }}：</span>
                    <i
                      class="el-icon-delete"
                      style="font-size: 25px; vertical-align: middle"
                      @click="RemoveRankEndReward(v1.rankAttackEndRewards, k2)"
                    ></i>
                    <ul>
                      <li>
                        <span class="enemyK">掉落物ID：</span>
                        <el-input
                          v-model="v2.fallID"
                          placeholder="请输入掉落物ID"
                          style="width: 50%"
                        ></el-input>
                      </li>
                      <li>
                        <span class="enemyK">掉落物名称：</span>
                        <el-input
                          v-model="v2.fallName"
                          placeholder="请输入掉落物名称"
                          style="width: 50%"
                        ></el-input>
                      </li>
                      <li>
                        <span class="enemyK">掉落数量：</span>
                        <el-input-number
                          class="enemyV"
                          v-model="v2.nums"
                          style="width: 50%"
                        ></el-input-number>
                      </li>
                      <li>
                        <span class="enemyK">掉落概率：</span>
                        <el-input-number
                          class="enemyV"
                          v-model="v2.fallProbability"
                          style="width: 50%"
                        ></el-input-number>
                      </li>
                    </ul>
                  </li>
                </ul>
              </li>
              <li class="itemname" slot="title" style="height: auto">
                <span>每日玩家积分排名区间：</span>
                <i
                  class="el-icon-plus"
                  style="font-size: 25px; vertical-align: middle"
                  @click="
                    addDailyNpcPlayerRankNumSection(
                      v1.dailyNpcPlayerRankNumSectionDatas
                        .dailyNpcPlayerRankNumSections
                    )
                  "
                ></i>
                <ul>
                  <li class="itemname" style="height: auto">
                    <span class="enemyK">npc玩家总数：</span>
                    <el-input-number
                      class="enemyV"
                      v-model.number="
                        v1.dailyNpcPlayerRankNumSectionDatas.totalNums
                      "
                      style="width: 50%"
                    ></el-input-number>
                  </li>
                  <li
                    v-for="(v2, k2) in v1.dailyNpcPlayerRankNumSectionDatas
                      .dailyNpcPlayerRankNumSections"
                    :key="k2"
                    class="itemname"
                    slot="title"
                    style="height: auto"
                  >
                    <span>区间{{ k2 + 1 }}：</span>
                    <i
                      class="el-icon-delete"
                      style="font-size: 25px; vertical-align: middle"
                      @click="
                        RemoveDailyNpcPlayerRankNumSection(
                          v1.dailyNpcPlayerRankNumSectionDatas
                            .dailyNpcPlayerRankNumSections,
                          k2
                        )
                      "
                    ></i>
                    <ul>
                      <li>
                        <span class="enemyK">积分区间：</span>
                        <el-input
                          v-model="v2.section"
                          placeholder="请输入掉落物ID"
                          style="width: 50%"
                        ></el-input>
                      </li>
                      <li>
                        <span class="enemyK">人数：</span>
                        <el-input-number
                          v-model="v2.nums"
                          placeholder="请输入掉落物名称"
                          style="width: 50%"
                        ></el-input-number>
                      </li>
                    </ul>
                  </li>
                </ul>
              </li>
              <li class="itemname" slot="title" style="height: auto">
                <span>每日排位结算奖励：</span>
                <i
                  class="el-icon-plus"
                  style="font-size: 25px; vertical-align: middle"
                  @click="addDailyRankEndReward(v1.dailyRankEndRewoards)"
                ></i>
                <ul>
                  <li
                    v-for="(v2, k2) in v1.dailyRankEndRewoards"
                    :key="k2"
                    class="itemname"
                    slot="title"
                    style="height: auto"
                  >
                    <span>掉落物{{ k2 + 1 }}：</span>
                    <i
                      class="el-icon-delete"
                      style="font-size: 25px; vertical-align: middle"
                      @click="
                        RemoveDailyRankEndReward(v1.dailyRankEndRewoards, k2)
                      "
                    ></i>
                    <ul>
                      <li>
                        <span class="enemyK">掉落物ID：</span>
                        <el-input
                          v-model="v2.fallID"
                          placeholder="请输入掉落物ID"
                          style="width: 50%"
                        ></el-input>
                      </li>
                      <li>
                        <span class="enemyK">掉落物名称：</span>
                        <el-input
                          v-model="v2.fallName"
                          placeholder="请输入掉落物名称"
                          style="width: 50%"
                        ></el-input>
                      </li>
                      <li>
                        <span class="enemyK">掉落数量：</span>
                        <el-input-number
                          class="enemyV"
                          v-model="v2.nums"
                          style="width: 50%"
                        ></el-input-number>
                      </li>
                      <li>
                        <span class="enemyK">玩家区间：</span>
                        <el-input
                          class="enemyV"
                          v-model="v2.section"
                          style="width: 50%"
                        ></el-input>
                      </li>
                    </ul>
                  </li>
                </ul>
              </li>
              <li class="itemname" slot="title" style="height: auto">
                <span class="enemyK">npc玩家等级区间：</span>
                <el-input
                  class="enemyV"
                  v-model="v1.npcPlayerLevelSection"
                  style="width: 50%"
                ></el-input>
              </li>
              <li class="itemname" slot="title" style="height: auto">
                <span class="enemyK">npc玩家装备质量：</span>
                <template>
                  <el-select
                    v-model="v1.npcPlayerEquipmentQuality"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </template>
              </li>
              <li class="itemname" slot="title" style="height: auto">
                <span class="enemyK">npc玩家装备等级区间：</span>
                <el-input
                  class="enemyV"
                  v-model="v1.npcPlayerEquipmentLevelSection"
                  style="width: 50%"
                ></el-input>
              </li>
              <li class="itemname" slot="title" style="height: auto">
                <span class="enemyK">npc玩家英雄区间：</span>
                <el-input
                  class="enemyV"
                  v-model="v1.npcPlayerHeroSection"
                  style="width: 50%"
                ></el-input>
              </li>
            </ul>
          </el-collapse-item>
        </el-collapse>
      </li>
    </ul>
  </el-container>
</template>
<style>
.levelcontainer {
  list-style: none;
  float: left;
  text-align: left;
  padding-left: 0;
  width: 100%;
  vertical-align: middle;
}
.levelcontainer li {
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 5px 0;
}
.itemname {
  width: 150px;
  float: left;
}
.itemvalue {
  float: left;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.enemyK {
  width: 150px;
  float: left;
  height: 40px;
  overflow: auto;
}
.enemyV {
  width: 100px;
  float: left;
}
.enemyLine {
  width: 100%;
  height: 40px;
  line-height: 40px;
}
</style>
<script>
import Vue from "vue";
import globaldata from "../GlobalData.js";
export default {
  data() {
    return {
      formKey: "ranksPool",
      options: [
        {
          value: 0,
          label: "绿色",
        },
        {
          value: 1,
          label: "蓝色",
        },
        {
          value: 2,
          label: "紫色",
        },
        {
          value: 3,
          label: "金色",
        },
        {
          value: 4,
          label: "红色",
        },
      ],
      curItem: "",
      itemData: {},
      commonRankconfigs: [
        { name: "每日固定积分", config: "dailyFixedRankNums" },
        { name: "每日挑战次数", config: "dailyChallengeNums" },
        { name: "每日视频挑战次数", config: "dailyVideoChallengeNums" },
        { name: "胜利积分", config: "victoryRankNums" },
        { name: "连胜积分", config: "victoryThanOneRankNums" },
        { name: "失败积分", config: "failRankNums" },
        { name: "连败积分", config: "failThanOneRankNums" },
        { name: "每日进阶数", config: "dailyAdvancedRankNums" },
        { name: "每日退阶数", config: "dailyRetrogressionRanlNums" },
      ],
    };
  },
  components: {},
  mounted: function () {
    this.itemData = global.gamedata[this.formKey];
    console.log(this.itemData);
    if (!this.itemData) {
      var v = {};
      //   for (let k in this.commonRankconfigs) {
      //     if (this.commonRankconfigs[k]["length"]) {
      //       let n = [];
      //       for (let i = 0; i < this.commonRankconfigs[k]["length"]; i++) {
      //         n.push(0);
      //       }
      //       v[this.commonRankconfigs[k]["config"]] = n;
      //     } else {
      //       v[this.commonRankconfigs[k]["config"]] = 0;
      //     }
      //   }
      Vue.set(global.gamedata, this.formKey, v);
      this.itemData = global.gamedata[this.formKey];
      //   if(this.itemData.Chapters){
      //       this.chapterNumber=this.itemData.Chapters.length;
      //   }
    }
  },
  methods: {
    addRanksPool: function () {
      if (!this.itemData.RankPools) {
        Vue.set(this.itemData, "RankPools", {});
      }
      this.$prompt("请输入段位池名称", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(({ value }) => {
          if (this.itemData.RankPools.hasOwnProperty(value)) {
            this.$message({
              type: "info",
              message: "已存在同名段位池",
            });
          } else {
            let rankFall = {
              rankName:"",
              rankAttackEndRewards: [],
              dailyRankEndRewoards: [],
              dailyNpcPlayerRankNumSectionDatas: {
                totalNums: 0,
                dailyNpcPlayerRankNumSections: [],
              },
              npcPlayerLevelSection: "1-1",
              npcPlayerEquipmentQuality: 0,
              npcPlayerEquipmentLevelSection: "1-1",
              npcPlayerHeroSection: "1-1",
            };
            Vue.set(this.itemData.RankPools, value, rankFall);
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消输入",
          });
        });
    },
    removeRanksPool: function (idx) {
      Vue.delete(this.itemData.RankPools, idx);
    },
    addRankEndReward: function (rankAttackEndRewards) {
      let rankAttackEndRewardData = {
        fallID: "",
        fallName: "",
        nums: 1,
        fallProbability: 0,
      };
      rankAttackEndRewards.push(rankAttackEndRewardData);
    },
    RemoveRankEndReward: function (rankAttackEndRewards, index) {
      rankAttackEndRewards.splice(index, 1);
    },
    addDailyRankEndReward: function (dailyRankEndRewoards) {
      let dailyRankEndRewardData = {
        fallID: "",
        fallName: "",
        nums: 1,
        section: "1-1",
      };
      dailyRankEndRewoards.push(dailyRankEndRewardData);
    },
    RemoveDailyRankEndReward: function (dailyRankEndRewoards, index) {
      dailyRankEndRewoards.splice(index, 1);
    },
    addDailyNpcPlayerRankNumSection: function (dailyNpcPlayerRankNumSections) {
      let dailyNpcPlayerRankNumSectionData = {
        section: "1-1",
        nums: 1,
      };
      dailyNpcPlayerRankNumSections.push(dailyNpcPlayerRankNumSectionData);
    },
    RemoveDailyNpcPlayerRankNumSection: function (
      dailyNpcPlayerRankNumSections,
      index
    ) {
      dailyNpcPlayerRankNumSections.splice(index, 1);
    },
    forceUpdate: function () {
      this.$forceUpdate();
    },
    changes: function () {},
  },
};
</script>