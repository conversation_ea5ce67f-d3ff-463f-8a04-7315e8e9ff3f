import { app, BrowserWindow, dialog } from 'electron'
import globaldata from '../renderer/GlobalData'
import '../renderer/store'
const ipcMain = require('electron').ipcMain;
/**
 * Set `__static` path to static files in production
 * https://simulatedgreg.gitbooks.io/electron-vue/content/en/using-static-assets.html
 */
if (process.env.NODE_ENV !== 'development') {
    global.__static = require('path').join(__dirname, '/static').replace(/\\/g, '\\\\')
}

let mainWindow

ipcMain.on('openDevTools', (event, args) => {
    console.log('openDevTools----');
    mainWindow.webContents.openDevTools({ mode: 'right' })
});
const winURL = process.env.NODE_ENV === 'development' ?
    `http://localhost:9080` :
    `file://${__dirname}/index.html`

function createWindow() {
    /**
     * Initial window options
     */
    mainWindow = new BrowserWindow({
        height: 563,
        useContentSize: true,
        width: 1000
    })

    mainWindow.loadURL(winURL)
    mainWindow.on('close', (e) => {
        e.preventDefault(); //阻止默认行为，一定要有
        dialog.showMessageBox({
            type: 'info',
            title: '退出应用',
            defaultId: 0,
            message: '退出前请手动保存已编辑的数据，是否继续退出应用？',
            buttons: ['是', '否']
        }, (index) => {
            if (index === 0) {
                // console.log("这里添加保存功能")
                // globaldata.saveDB().then(() => {
                //     mainWindow = null;
                //     app.exit;
                // }).catch((err) => {
                //     console.log(err)
                //     dialog.showMessageBox({
                //         type: "info",
                //         title: "提示",
                //         message: err
                //     })
                // });
                mainWindow = null;
                app.exit(); //exit()直接关闭客户端，不会执行quit();  
            } else {
                // mainWindow = null;
                // app.exit(); //exit()直接关闭客户端，不会执行quit();  
            }
        })
    });

    mainWindow.on('closed', () => {
        mainWindow = null
    })
}

app.on('ready', createWindow)

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit()
    }
})

app.on('activate', () => {
    if (mainWindow === null) {
        createWindow()
    }
})

/**
 * Auto Updater
 *
 * Uncomment the following code below and install `electron-updater` to
 * support auto updating. Code Signing with a valid certificate is required.
 * https://simulatedgreg.gitbooks.io/electron-vue/content/en/using-electron-builder.html#auto-updating
 */

/*
import { autoUpdater } from 'electron-updater'

autoUpdater.on('update-downloaded', () => {
  autoUpdater.quitAndInstall()
})

app.on('ready', () => {
  if (process.env.NODE_ENV === 'production') autoUpdater.checkForUpdates()
})
 */