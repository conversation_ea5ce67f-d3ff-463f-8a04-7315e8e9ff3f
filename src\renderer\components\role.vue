<template>
  <mylistview :formKey="formKey" v-on:setItemData="getItemData">
    <ul class="levelcontainer">
      <li>
        <div class="enemyLine">
          <span class="enemyK">名字:</span>
          <el-input
            class="enemyV"
            v-model="itemData.name"
            placeholder="请输入角色名称"
            style="width: 50%"
          ></el-input>
        </div>
      </li>
      <li>
        <span class="enemyK">角色类型：</span>
        <template>
          <el-select v-model="itemData.roleType" placeholder="请选择">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
      </li>
      <li>
        <div class="enemyK">品质:</div>
        <!-- <span>{{ itemData.skybox }}</span> -->
        <el-dropdown class="itemvalue" @command="selectQualityBox">
          <span class="el-dropdown-link">
            {{ qualityStr }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(value, idx) in qualityType"
              :command="idx"
              :key="idx"
              >{{ value }}</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </li>
      <li v-for="(value, key) in roleconfigs" :key="key">
        <div class="enemyLine">
          <span class="enemyK">{{ value.name }}</span>
          <el-input
            v-if="value.type === 'string'"
            class="enemyV"
            v-model="itemData[value.config]"
            style="width: 50%"
          ></el-input>

          <el-input
            v-else
            class="enemyV"
            type="number"
            v-model.number="itemData[value.config]"
            style="width: 50%"
          ></el-input>
        </div>
      </li>
      <li v-for="(value, key) in skillsconfigs" :key="key">
        <div class="enemyLine" v-if="itemData.skills != null">
          <span class="enemyK">{{ value.name }}</span>
          <span class="skillkey">基础值:</span>
          <el-input-number
            class="skillvalue"
            @input="forceUpdate"
            type="number"
            :min="0"
            v-model.number="itemData.skills[key].attack"
          ></el-input-number>
          <span class="skillkey">增量:</span>
          <el-input-number
            class="skillvalue"
            @input="forceUpdate"
            type="number"
            :min="0"
            v-model.number="itemData.skills[key].addattack"
          ></el-input-number>
        </div>
        <div
          class="enemyLine"
          style="float: left"
          v-if="itemData.skills != null"
        >
          <span class="enemyK" style="font-size: 12px"
            >冷却增量填正数，计算时自动扣减</span
          >
          <span class="skillkey">冷却:</span>
          <el-input-number
            class="skillvalue"
            @input="forceUpdate"
            type="number"
            :min="0"
            v-model.number="itemData.skills[key].cd"
          ></el-input-number>
          <span class="skillkey">冷却增量:</span>
          <el-input-number
            class="skillvalue"
            @input="forceUpdate"
            type="number"
            :min="0"
            v-model.number="itemData.skills[key].addcd"
          ></el-input-number>
        </div>
        <div
          class="enemyLine"
          v-if="itemData.skills != null"
          style="float: left"
        >
          <span class="skillkey">技能名称:</span>
          <el-input
            class="skillvalue"
            @input="forceUpdate"
            v-model="itemData.skills[key].activeSkillName"
            style="width: 70%"
          ></el-input>
        </div>
        <div
          class="enemyLine"
          v-if="itemData.skills != null"
          style="float: left"
        >
          <span class="skillkey">技能描述:</span>
          <el-input
            class="skillvalue"
            @input="forceUpdate"
            v-model="itemData.skills[key].activeSkillDesc"
            style="width: 70%"
          ></el-input>
        </div>
      </li>
      <li>
        <div class="enemyLine">
          <span class="enemyK">技能切换人物ID:</span>
          <div class="itemvalue">
            <div class="form-row">
              <i class="el-icon-plus operation-btn" @click="addSkillSwitch"></i>
            </div>
            <div v-for="(id, index) in itemData.skillSwitch" :key="index" class="reward-item">
              <el-input v-model="itemData.skillSwitch[index]" @input="forceUpdate" placeholder="请输入人物ID" style="width: 200px"></el-input>
              <i class="el-icon-delete operation-btn" @click="removeSkillSwitch(index)"></i>
            </div>
          </div>
        </div>
      </li>
    </ul>
  </mylistview>
</template>

<style>
.skillkey {
  float: left;
  width: 80px;
}
.skillvalue {
  float: left;
}
.form-row {
  display: flex;
  align-items: center;
  margin: 10px 0;
}
.reward-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 5px 0;
}
.operation-btn {
  margin-left: 10px;
  cursor: pointer;
}
</style>

<script>
import Vue from 'vue';
import mylistview from "./MyListView.vue";
const qualityType = ["普通", "稀有", "卓越", "史诗", "传说", "魔王"];
// const skillprefab = { attack: 0, addattack: 0, cd: 0, addcd: 0 };
export default {
  data() {
    return {
      formKey: "role",
      curItem: "",
      itemData: {},
      qualityStr: "普通",
      qualityType: qualityType,
      roleconfigs: [
        { name: "基础血量", config: "hp" },
        { name: "血量增量", config: "addhp" },
        // { name: "基础蓝量", config: "mp" },
        // { name: "蓝量增量", config: "addmp" },
        { name: "基础攻击力", config: "attack" },
        { name: "攻击力增量", config: "addattack" },
        { name: "基础防御", config: "def" },
        { name: "防御增量", config: "adddef" },
        // { name: "基础命中", config: "hit" },
        // { name: "命中增量", config: "addhit" },
        { name: "基础闪避", config: "dodge" },
        { name: "闪避增量", config: "adddodge" },
        { name: "基础暴击", config: "crit" },
        { name: "暴击增量", config: "addcrit" },
        { name: "基础穿刺", config: "puncture" },
        { name: "穿刺增量", config: "addpuncture" },
        { name: "进阶需要的碎片ID", config: "debris", type: "string" },
        { name: "被动技能名称", config: "passiveSkillName", type: "string" },
        { name: "被动技能描述", config: "passiveSkillDesc", type: "string" },
        { name: "被动技能BuffID", config: "passiveBuff", type: "string" },
        { name: "礼包ID", config: "giftID", type: "string" },
        { name: "出场音效", config: "showMusic", type: "string" },
        { name: "技能icon", config: "skillIcon", type: "string" },

        // {
        //   name: "被动类型(血量-0，攻击-1，防御-2，闪避-3，暴击-4，穿刺-5,冷却-6)",
        //   config: "passive",
        //   type: "string",
        // },
        // { name: "被动技能数值", config: "addpassive", type: "string" },
        // { name: "技能一基础值", config: "skill1" },
        // { name: "技能一增量", config: "addskill1" },
        // { name: "技能二基础值", config: "skill2" },
        // { name: "技能二增量", config: "addskill2" },
        // { name: "技能三基础值", config: "skill3" },
        // { name: "技能三增量", config: "addskill3" },
        // { name: "技能四基础值", config: "skill4" },
        // { name: "技能四增量", config: "addskill4" },
        // { name: "技能五基础值", config: "skill5" },
        // { name: "技能五增量", config: "addskill5" },
        // { name: "升级需要的经验值", config: "exp" },
        // { name: "升级经验值增量", config: "addexp" },
        // { name: "进阶需要的碎片ID", config: "debris", type: "string" },
        // { name: "进阶碎片增量", config: "adddebris" },
        // { name: "进阶需要的金币", config: "gold" },
        // { name: "进阶金币增量", config: "addgold" },
        // { name: "基础速度", config: "speed" },
        // { name: "速度增量", config: "addspeed" },
        // { name: "每秒回血", config: "hp1s" },
        // { name: "回血增量", config: "addhp1s" },
        // { name: "每秒回蓝", config: "mp1s" },
        // { name: "回蓝增量", config: "addmp1s" },
      ],
      skillsconfigs: [
        { name: "技能一", id: "1" },
        { name: "技能二", id: "2" },
        { name: "技能三", id: "3" },
        { name: "技能四", id: "4" },
        { name: "技能五", id: "5" },
      ],
      options: [
        {
          value: 0,
          label: "玩家角色",
        },
        {
          value: 1,
          label: "敌人",
        },
      ],
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      this.itemData = data;
      console.log("getItemData", data);
      if (this.itemData) {
        this.qualityStr = qualityType[this.itemData.quality] || "系统";
        if (!this.itemData.skills || this.itemData.skills.length == 0) {
          this.itemData.skills = [];
          for (let i = 0, len = this.skillsconfigs.length; i < len; i++) {
            this.itemData.skills.push({
              attack: 0,
              addattack: 0,
              cd: 0,
              addcd: 0,
              activeSkillName: "",
              activeSkillDesc: "",
            });
          }
        }
        if (!this.itemData.skillSwitch) {
          Vue.set(this.itemData, 'skillSwitch', []);
        }
      } else {
        this.itemData = { 
          skills: [],
          skillSwitch: []
        };
        for (let i = 0, len = this.skillsconfigs.length; i < len; i++) {
          this.itemData.skills.push({
            attack: 0,
            addattack: 0,
            cd: 0,
            addcd: 0,
            activeSkillName: "",
            activeSkillDesc: "",
          });
        }
      }
      console.log("ItemData", this.itemData);
    },
    selectQualityBox: function (command) {
      // console.log("click on item " + command);
      this.itemData.quality = parseInt(command);
      this.qualityStr = qualityType[command];
    },
    forceUpdate: function () {
      this.$forceUpdate();
    },
    addSkillSwitch: function() {
      if (!this.itemData.skillSwitch) {
        this.itemData.skillSwitch = [];
      }
      this.itemData.skillSwitch.push("");
      this.$forceUpdate();
    },
    removeSkillSwitch: function(index) {
      this.itemData.skillSwitch.splice(index, 1);
      this.$forceUpdate();
    },
  },
};
</script>