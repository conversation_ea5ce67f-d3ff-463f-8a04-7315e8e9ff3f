<template>
  <mylistview :formKey="formKey" v-on:setItemData="getItemData">
    <ul class="levelcontainer">
      <li>
        <span class="enemyK">是否有首通奖励：</span>
        <template>
          <el-radio
            v-model="itemData.IsHaveFitstReward"
            label="true"
            @change="RefreshFirstReword"
            >是</el-radio
          >
          <el-radio
            v-model="itemData.IsHaveFitstReward"
            label="false"
            @change="RefreshFirstReword"
            >否</el-radio
          >
        </template>
      </li>
      <li v-if="itemData.IsHaveFitstReward == 'true'">
        <div class="itemname">奖励类型:</div>
        <el-dropdown class="itemvalue" @command="selectFirstRewardType">
          <span class="el-dropdown-link">
            {{ firstRewardTypeStr
            }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(value, idx) in firstRewardType"
              :command="idx"
              :key="idx"
              >{{ value }}</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </li>
      <li v-if="itemData.IsHaveFitstReward == 'true'">
        <div class="itemname">奖励物品名:</div>
        <el-input
          class="enemyV"
          v-model.number="itemData.firstRewardName"
          style="width: 50%"
        ></el-input>
      </li>
      <li v-if="itemData.IsHaveFitstReward == 'true'">
        <div class="itemname">奖励数量:</div>
        <el-input
          class="enemyV"
          type="number"
          v-model.number="itemData.firstRewardNum"
          style="width: 50%"
        ></el-input>
      </li>
      <li>
        <div class="itemname">关卡时间限制:</div>
        <el-input
          class="enemyV"
          v-model.number="itemData.levelTimeLimit"
          style="width: 50%"
        ></el-input>
      </li>
      <li>
        <div class="itemname">关卡默认消耗数量:</div>
        <el-input
          class="enemyV"
          v-model.number="itemData.levelDefaultSpend"
          style="width: 50%"
        ></el-input>
      </li>
      <li>
        <div class="itemname">关卡最小消耗数量:</div>
        <el-input
          class="enemyV"
          v-model.number="itemData.levelMinSpend"
          style="width: 50%"
        ></el-input>
      </li>
      <li>
        <div class="itemname">关卡最大消耗数量:</div>
        <el-input
          class="enemyV"
          v-model.number="itemData.levelMaxSpend"
          style="width: 50%"
        ></el-input>
      </li>
      <li>
        <div class="itemname">关卡消耗增量:</div>
        <el-input
          class="enemyV"
          v-model.number="itemData.levelSpendAdd"
          style="width: 50%"
        ></el-input>
      </li>
      <li style="height: 40px">
        <span class="enemyK">概率区间长度：</span>
        <el-input
          class="enemyV"
          type="number"
          v-model.number="itemData.multipleIntervalLength"
          :min="0"
          style="width: 50%"
          @change="ChangemultipleInterval(itemData.multipleIntervalLength)"
        ></el-input>
      </li>
      <li v-if="itemData.multipleIntervalLength != 0">
        <span class="enemyK">概率区间：</span>
        <ul>
          <li
            v-for="(value, index) in itemData.multipleInterval"
            :key="index"
            class="itemname"
            style="height: 40px"
          >
            <span class="enemyK">{{ index }}-{{ index + 1 }}</span>
            <el-input
              type="number"
              v-model.number="itemData.multipleInterval[index]"
              style="width: 50%"
            ></el-input>
          </li>
        </ul>
      </li>
      <li style="height: auto">
        <div class="itemname">普通英雄池：</div>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addCommonHeroPool"
        ></i>
        <ul>
          <li
            v-for="(v1, k1) in itemData.commonHeroPool"
            :key="k1"
            class="itemname"
            slot="title"
            style="height: 40px"
          >
            <span>普通英雄{{ k1 + 1 }}名称：</span>
            <el-input
              v-model="itemData.commonHeroPool[k1]"
              placeholder="请输入普通英雄名"
              style="width: 50%"
            ></el-input>
            <!-- <i class="el-icon-more"></i> -->
            <i
              class="el-icon-delete"
              style="font-size: 25px; vertical-align: middle"
              @click="removeCommonHeroPool(k1)"
            ></i>
          </li>
        </ul>
      </li>
      <li style="height: auto">
        <div class="itemname">特殊英雄池：</div>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addSpecialHeroPool"
        ></i>
        <ul>
          <li
            v-for="(v1, k1) in itemData.specialHeroPool"
            :key="k1"
            class="itemname"
            slot="title"
            style="height: 40px"
          >
            <span>特殊英雄{{ k1 + 1 }}名称：</span>
            <el-input
              v-model="itemData.specialHeroPool[k1]"
              placeholder="请输入特殊英雄名"
              style="width: 50%"
            ></el-input>
            <!-- <i class="el-icon-more"></i> -->
            <i
              class="el-icon-delete"
              style="font-size: 25px; vertical-align: middle"
              @click="removeSpecialHeroPool(k1)"
            ></i>
          </li>
        </ul>
      </li>
      <li>
        <div class="itemname">掉落物：</div>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addfallingGoods1"
        ></i>
        <ul>
          <li
            v-for="(v1, k1) in itemData.fallingGoods"
            :key="k1"
            class="itemname"
            slot="title"
            style="height: auto"
          >
            <span>掉落物{{ k1 + 1 }}：</span>
            <!-- <i class="el-icon-more"></i> -->
            <i
              class="el-icon-delete"
              style="font-size: 25px; vertical-align: middle"
              @click="removefallingGoods(k1)"
            ></i>
            <ul>
              <li>
                <span>掉落物ID：</span>
                <el-input
                  v-model="itemData.fallingGoods[k1].fallingGoodsID"
                  placeholder="请输入掉落物ID"
                  style="width: 50%"
                ></el-input>
              </li>
              <li>
                <span>掉落概率：</span>
                <el-input-number
                  v-model="itemData.fallingGoods[k1].fallingProbability"
                  placeholder="请输入掉落概率"
                  style="width: 50%"
                ></el-input-number>
              </li>
              <li>
                <span>最小掉落数量：</span>
                <el-input-number
                  v-model="itemData.fallingGoods[k1].fallingGoodsMinNum"
                  placeholder="请输入最小掉落数量"
                  style="width: 50%"
                ></el-input-number>
              </li>
              <li>
                <span>最大掉落数量：</span>
                <el-input-number
                  v-model="itemData.fallingGoods[k1].fallingGoodsMaXNum"
                  placeholder="请输入最大掉落数量"
                  style="width: 50%"
                ></el-input-number>
              </li>
            </ul>
          </li>
        </ul>
      </li>
    </ul>
  </mylistview>
</template>
<style>
.levelcontainer {
  list-style: none;
  float: left;
  text-align: left;
  padding-left: 0;
  width: 100%;
  vertical-align: middle;
}
.levelcontainer li {
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 5px 0;
}
.itemname {
  width: 150px;
  float: left;
}
.itemvalue {
  float: left;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.enemyK {
  width: 150px;
  float: left;
  height: 40px;
  overflow: auto;
}
.enemyV {
  width: 100px;
  float: left;
}
.enemyLine {
  width: 100%;
  height: 40px;
  line-height: 40px;
}
</style>
<script>
import mylistview from "./MyListView.vue";
import Vue from "vue";
import globaldata from "../GlobalData.js";
const firstRewardType=["金币", "钻石", "装备", "道具"];
export default {
  data() {
    return {
      formKey: "specialLevel",
      curItem: "",
      itemData: {},
      firstRewardTypeStr: "金币",
      firstRewardType:firstRewardType,
      IsHaveFitstReward: false,
      IntervalLength: 1,
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      this.itemData = data;
      if (!this.itemData.multipleInterval) {
        Vue.set(this.itemData, "multipleInterval", []);
      }
      if (!this.itemData.IsHaveFitstReward) {
        Vue.set(this.itemData, "IsHaveFitstReward", "false");
      }
      if (!this.itemData.multipleIntervalLength) {
        Vue.set(this.itemData, "multipleIntervalLength", 0);
      }
      if (!this.itemData.levelTimeLimit) {
        Vue.set(this.itemData, "levelTimeLimit", 0);
      }
      if (!this.itemData.levelDefaultSpend) {
        Vue.set(this.itemData, "levelDefaultSpend", 0);
      }
      if (!this.itemData.levelMinSpend) {
        Vue.set(this.itemData, "levelMinSpend", 0);
      }
      if (!this.itemData.levelMaxSpend) {
        Vue.set(this.itemData, "levelMaxSpend", 0);
      }
      if (!this.itemData.levelSpendAdd) {
        Vue.set(this.itemData, "levelSpendAdd", 0);
      }
      if(this.itemData){
        this.firstRewardTypeStr = this.firstRewardType[this.itemData.firstRewardType]||"金币";
      }
    },
    selectFirstRewardType: function (command) {
      this.itemData.firstRewardType = parseInt(command);
      this.firstRewardTypeStr = this.firstRewardType[command];
    },
    RefreshFirstReword: function () {
      if (this.itemData.IsHaveFitstReward == "false") {
        delete this.itemData.firstRewardNum;
        delete this.itemData.firstRewardType;
        delete this.itemData.firstRewardName;
        this.firstRewardTypeStr = "金币";
      }
    },
    ChangemultipleInterval: function (length) {
      if (this.itemData.multipleInterval.length < length) {
        for (let i = this.itemData.multipleInterval.length; i < length; i++) {
          this.itemData.multipleInterval.push(0);
        }
      } else if (this.itemData.multipleInterval.length > length) {
        for (let i = this.itemData.multipleInterval.length; i > length; i--) {
          this.itemData.multipleInterval.pop();
        }
      }
      console.log("vvvvvvvv", this.itemData.multipleInterval.length);
    },
    ChangefallingGoodsInterval: function (index,length) {
      if (this.itemData.fallingGoods[index].fallingProbabilityInterval.length < length) {
        for (let i = this.itemData.fallingGoods[index].fallingProbabilityInterval.length; i < length; i++) {
          this.itemData.fallingGoods[index].fallingProbabilityInterval.push(0);
        }
      } else if (this.itemData.fallingGoods[index].fallingProbabilityInterval.length > length) {
        for (let i = this.itemData.fallingGoods[index].fallingProbabilityInterval.length; i > length; i--) {
          this.itemData.fallingGoods[index].fallingProbabilityInterval.pop();
        }
      }
      console.log("vvvvvvvv", this.itemData.fallingGoods[index].fallingProbabilityInterval.length);
    },
    addfallingGoods1: function () {
      let fallingGoods = {
        fallingGoodsID: "",
        fallingProbability:0,
        fallingGoodsMinNum: 0,
        fallingGoodsMaXNum: 0,
      };
      if (
        !this.itemData.fallingGoods ||
        this.itemData.fallingGoods.length == 0
      ) {
        Vue.set(this.itemData, "fallingGoods", [fallingGoods]);
      } else {
        this.itemData.fallingGoods.push(fallingGoods);
      }
    },
    removefallingGoods: function (idx) {
      this.itemData.fallingGoods.splice(idx, 1);
    },
    addCommonHeroPool: function () {
      let commonHero = "";
      if (
        !this.itemData.commonHeroPool ||
        this.itemData.commonHeroPool.length == 0
      ) {
        Vue.set(this.itemData, "commonHeroPool", [commonHero]);
      } else {
        this.itemData.commonHeroPool.push(commonHero);
      }
    },
    removeCommonHeroPool: function (idx) {
      this.itemData.commonHeroPool.splice(idx, 1);
    },
    addSpecialHeroPool: function () {
      let specialHero = "";
      if (
        !this.itemData.specialHeroPool ||
        this.itemData.specialHeroPool.length == 0
      ) {
        Vue.set(this.itemData, "specialHeroPool", [specialHero]);
      } else {
        this.itemData.specialHeroPool.push(specialHero);
      }
    },
    removeSpecialHeroPool: function (idx) {
      this.itemData.specialHeroPool.splice(idx, 1);
    },
  },
};
</script>