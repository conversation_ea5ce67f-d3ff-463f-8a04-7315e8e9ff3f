<style>
.activitycontainer {
    list-style: none;
    padding-left: 0;
    width: 100%;
    overflow: hidden;
}

.activitycontainer li {
    width: 100%;
    min-height: 40px;
    margin: 5px 0;
    clear: both;
}

.itemname {
    width: 150px;
    float: left;
    line-height: 40px;
}

.itemvalue {
    width: 50%;
    float: left;
}

/* 礼包项样式 */
.gift-item {
    border: 1px solid #eee;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    width: 100%;
}

/* 表单项样式 */
.form-row {
    display: flex;
    align-items: center;
    margin: 10px 0;
}

.form-label {
    width: 80px;
    flex-shrink: 0;
}

/* 奖励项样式 */
.reward-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 5px 0;
}

/* 操作按钮样式 */
.operation-btn {
    margin-left: 10px;
    cursor: pointer;
}
</style>

<template>
    <mylistview :formKey="formKey" @setItemData="getItemData">
        <div class="activitycontainer">
            <!-- 基本信息 -->
            <div class="form-row">
                <div class="itemname">名称:</div>
                <el-input class="itemvalue" v-model="itemData.name" placeholder="请输入活动礼包名称"></el-input>
            </div>

            <div class="form-row">
                <div class="itemname">描述:</div>
                <el-input class="itemvalue" type="textarea" v-model="itemData.description"
                    placeholder="请输入活动礼包描述"></el-input>
            </div>
            <!-- 免费礼包 -->
            <div class="gift-item">
                <div class="form-row">
                    <h3 style="margin: 0;">免费礼包</h3>
                </div>
                <div class="form-row">
                    <span style="margin: 0 10px;">限购次数(0为不限)：</span>
                    <el-input type="number" v-model.number="itemData.freeGift.purchaseCount" placeholder="限购次数"
                        style="width: 100px">
                    </el-input>
                </div>
                <div class="form-row">
                    <div class="form-label">奖励内容：</div>
                    <i class="el-icon-plus operation-btn" @click="addReward('freeGift')"></i>
                </div>
                <div v-for="(reward, rIndex) in itemData.freeGift.reward" :key="rIndex" class="reward-item">
                    <el-select v-model="reward.id" clearable filterable allow-create placeholder="奖励ID"
                        class="product-input" style="width: 200px;">
                        <el-option v-for="(item, idx) in CommonlyArticles" :key="idx" :label="getArticleName(item)"
                            :value="item" />
                    </el-select>
                    <el-input type="number" v-model.number="reward.count" placeholder="数量" style="width: 100px">
                    </el-input>
                    <i class="el-icon-delete operation-btn" @click="removeReward('freeGift', rIndex)"></i>
                    <span>{{getArticleName(reward.id)}}</span>
                </div>
            </div>

            <!-- 普通礼包 -->
            <div class="gift-item">
                <div class="form-row">
                    <h3 style="margin: 0;">普通礼包</h3>
                </div>
                <div class="form-row">
                    <span style="margin: 0 10px;">计费ID：</span>
                    <el-input v-model="itemData.commonGift.payID" placeholder="请输入计费ID" style="width: 200px">
                    </el-input>
                </div>
                <div class="form-row">
                    <span style="margin: 0 10px;">限购次数(0为不限)：</span>
                    <el-input type="number" v-model.number="itemData.commonGift.purchaseCount" placeholder="限购次数" style="width: 100px">
                    </el-input>
                </div>
                <div class="form-row">
                    <div class="form-label">奖励内容：</div>
                    <i class="el-icon-plus operation-btn" @click="addReward('commonGift')"></i>
                </div>
                <div v-for="(reward, rIndex) in itemData.commonGift.reward" :key="rIndex" class="reward-item">
                    <el-select v-model="reward.id" clearable filterable allow-create placeholder="奖励ID"
                        class="product-input" style="width: 200px;">
                        <el-option v-for="(item, idx) in CommonlyArticles" :key="idx" :label="getArticleName(item)"
                            :value="item" />
                    </el-select>
                    <el-input type="number" v-model.number="reward.count" placeholder="数量" style="width: 100px">
                    </el-input>
                    <i class="el-icon-delete operation-btn" @click="removeReward('commonGift', rIndex)"></i>
                    <span>{{getArticleName(reward.id)}}</span>
                </div>
            </div>

            <!-- 高级礼包 -->
            <div class="gift-item">
                <div class="form-row">
                    <h3 style="margin: 0;">高级礼包</h3>
                </div>
                <div class="form-row">
                    <span style="margin: 0 10px;">计费ID：</span>
                    <el-input v-model="itemData.advancedGift.payID" placeholder="请输入计费ID" style="width: 200px">
                    </el-input>
                </div>
                <div class="form-row">
                    <span style="margin: 0 10px;">限购次数(0为不限)：</span>
                    <el-input type="number" v-model.number="itemData.advancedGift.purchaseCount" placeholder="限购次数" style="width: 100px">
                    </el-input>
                </div>
                <div class="form-row">
                    <div class="form-label">奖励内容：</div>
                    <i class="el-icon-plus operation-btn" @click="addReward('advancedGift')"></i>
                </div>
                <div v-for="(reward, rIndex) in itemData.advancedGift.reward" :key="rIndex" class="reward-item">
                    <el-select v-model="reward.id" clearable filterable allow-create placeholder="奖励ID"
                        class="product-input" style="width: 200px;">
                        <el-option v-for="(item, idx) in CommonlyArticles" :key="idx" :label="getArticleName(item)"
                            :value="item" />
                    </el-select>
                    <el-input type="number" v-model.number="reward.count" placeholder="数量" style="width: 100px">
                    </el-input>
                    <i class="el-icon-delete operation-btn" @click="removeReward('advancedGift', rIndex)"></i>
                    <span>{{getArticleName(reward.id)}}</span>
                </div>
            </div>

            <!-- 至臻礼包 -->
            <div class="gift-item">
                <div class="form-row">
                    <h3 style="margin: 0;">至臻礼包</h3>
                </div>
                <div class="form-row">
                    <span style="margin: 0 10px;">计费ID：</span>
                    <el-input v-model="itemData.perfectestGift.payID" placeholder="请输入计费ID" style="width: 200px">
                    </el-input>
                </div>
                <div class="form-row">
                    <span style="margin: 0 10px;">限购次数(0为不限)：</span>
                    <el-input type="number" v-model.number="itemData.perfectestGift.purchaseCount" placeholder="限购次数" style="width: 100px">
                    </el-input>
                </div>
                <div class="form-row">
                    <div class="form-label">奖励内容：</div>
                    <i class="el-icon-plus operation-btn" @click="addReward('perfectestGift')"></i>
                </div>
                <div v-for="(reward, rIndex) in itemData.perfectestGift.reward" :key="rIndex" class="reward-item">
                    <el-select v-model="reward.id" clearable filterable allow-create placeholder="奖励ID"
                        class="product-input" style="width: 200px;">
                        <el-option v-for="(item, idx) in CommonlyArticles" :key="idx" :label="getArticleName(item)"
                            :value="item" />
                    </el-select>
                    <el-input type="number" v-model.number="reward.count" placeholder="数量" style="width: 100px">
                    </el-input>
                    <i class="el-icon-delete operation-btn" @click="removeReward('perfectestGift', rIndex)"></i>
                    <span>{{getArticleName(reward.id)}}</span>
                </div>
            </div>
        </div>
    </mylistview>
</template>

<script>
import Vue from "vue";
import mylistview from "./MyListView.vue";
export default {
    data() {
        return {
            formKey: "newHeroGift",
            CommonlyArticles: [],
            itemData: {
                name: "",
                description: "",
                freeGift: {
                    purchaseCount: 1,
                    reward: []
                },
                commonGift: {
                    payID: "",
                    purchaseCount: 1,
                    reward: []
                },
                advancedGift: {
                    payID: "",
                    purchaseCount: 1,
                    reward: []
                },
                perfectestGift: {
                    payID: "",
                    purchaseCount: 1,
                    reward: []
                }
            }
        };
    },
    components: {
        mylistview,
    },
    methods: {
        getItemData(data) {
            if (data) {
                // 删除可能存在的price字段
                if (data.commonGift && data.commonGift.price) delete data.commonGift.price;
                if (data.advancedGift && data.advancedGift.price) delete data.advancedGift.price;
                if (data.perfectestGift && data.perfectestGift.price) delete data.perfectestGift.price;
                this.itemData = data;
            } else {
                this.itemData = {
                    name: "",
                    description: "",
                    freeGift: {
                        purchaseCount: 1,
                        reward: []
                    },
                    commonGift: {
                        payID: "",
                        purchaseCount: 1,
                        reward: []
                    },
                    advancedGift: {
                        payID: "",
                        purchaseCount: 1,
                        reward: []
                    },
                    perfectestGift: {
                        payID: "",
                        purchaseCount: 1,
                        reward: []
                    }
                };
            }
            this.CommonlyArticles = global.gamedata["CommonlyArticles"] || [];
            if (!this.itemData.freeGift) Vue.set(this.itemData, "freeGift", { purchaseCount: 1, reward: [] });
            if (!this.itemData.commonGift) Vue.set(this.itemData, "commonGift", { payID: "", purchaseCount: 1, reward: [] });
            if (!this.itemData.advancedGift) Vue.set(this.itemData, "advancedGift", { payID: "", purchaseCount: 1, reward: [] });
            if (!this.itemData.perfectestGift) Vue.set(this.itemData, "perfectestGift", { payID: "", purchaseCount: 1, reward: [] });
        },
        removeGift(index) {
            // this.itemData.gifts.splice(index, 1);
        },
        addReward(giftName) {
            const reward = {
                id: "",
                count: 1
            };
            if (!this.itemData[giftName].reward) {
                Vue.set(this.itemData[giftName], "reward", [reward]);
            } else {
                this.itemData[giftName].reward.push(reward);
            }
        },
        removeReward(giftName, rewardIndex) {
            this.itemData[giftName].reward.splice(rewardIndex, 1);
        },
        handleRewardInput(value, reward) {
            if (value && !this.CommonlyArticles.includes(value)) {
                reward.id = value;
            }
        },
        getArticleName(articleId) {
            let _article = global.gamedata["article"][articleId];
            if (!_article) return "";
            return _article.name;
        }
    },
};
</script>