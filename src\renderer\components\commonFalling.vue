
<template>
  <el-container :formKey="formKey">
    <ul class="levelcontainer">
      <li v-for="(value, key) in commonFallingconfigs" :key="key">
        <div class="" v-if="value.length">
          <span class="enemyLine" style="float: left">{{ value.name }}</span>
          <div
            class="enemyLine"
            style="float: left"
            v-for="idx in value.length"
            :key="idx"
          >
            <span class="enemyK">{{ idx }}</span>
            <el-input
              v-if="value.type === 'string'"
              class="enemyV"
              v-model="itemData[value.config][idx - 1]"
              style="width: 50%"
            ></el-input>

            <el-input-number
              v-else
              class="enemyV"
              v-model.number="itemData[value.config][idx - 1]"
              style="width: 50%"
            ></el-input-number>
          </div>
        </div>
        <div class="enemyLine" style="float: left" v-else>
          <span class="enemyK">{{ value.name }}</span>
          <el-input
            v-if="value.type === 'string'"
            class="enemyV"
            v-model="itemData[value.config]"
            style="width: 50%"
          ></el-input>

          <el-input-number
            v-else
            class="enemyV"
            v-model.number="itemData[value.config]"
            style="width: 50%"
          ></el-input-number>
        </div>
      </li>
      <li
        class="itemname"
        style="height: auto"
      >
        <span>装备出售价格：</span>
        <ul>
          <li>
            <span class="enemyK">绿色出售价格：</span>
            <el-input
              v-model="itemData.equipSellPrice.green"
              type="number"
              style="width: 50%"
            ></el-input>
          </li>
          <li>
            <span class="enemyK">蓝色出售价格：</span>
            <el-input
              v-model="itemData.equipSellPrice.blue"
              type="number"
              style="width: 50%"
            ></el-input>
          </li>
          <li>
            <span class="enemyK">紫色出售价格：</span>
            <el-input
              v-model="itemData.equipSellPrice.violet"
              type="number"
              style="width: 50%"
            ></el-input>
          </li>
          <li>
            <span class="enemyK">橙色出售价格：</span>
            <el-input
              v-model="itemData.equipSellPrice.orange"
              type="number"
              style="width: 50%"
            ></el-input>
          </li>
          <li>
            <span class="enemyK">红色出售价格：</span>
            <el-input
              v-model="itemData.equipSellPrice.red"
              type="number"
              style="width: 50%"
            ></el-input>
          </li>
        </ul>
      </li>
      <li
        class="itemname"
        style="height: auto"
      >
        <span>源晶出售价格：</span>
        <ul>
          <li>
            <span class="enemyK">蓝色出售价格：</span>
            <el-input
              v-model="itemData.crystalSellPrice.blue"
              type="number"
              style="width: 50%"
            ></el-input>
          </li>
          <li>
            <span class="enemyK">紫色出售价格：</span>
            <el-input
              v-model="itemData.crystalSellPrice.violet"
              type="number"
              style="width: 50%"
            ></el-input>
          </li>
          <li>
            <span class="enemyK">橙色出售价格：</span>
            <el-input
              v-model="itemData.crystalSellPrice.orange"
              type="number"
              style="width: 50%"
            ></el-input>
          </li>
          <li>
            <span class="enemyK">红色出售价格：</span>
            <el-input
              v-model="itemData.crystalSellPrice.red"
              type="number"
              style="width: 50%"
            ></el-input>
          </li>
        </ul>
      </li>
      <li
        class="itemname"
        style="height: auto"
      >
        <span>章节通关时间：</span>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addChapterThroughTime"
        ></i>
        <ul>
          <li v-for="(value,index) in itemData.chapterThroughTime" :key="index">
            <span class="enemyK">章节{{index+1}}通关时间：</span>
            <el-input
              v-model="itemData.chapterThroughTime[index]"
              type="number"
              style="width: 50%"
            ></el-input>
            <i
              class="el-icon-delete"
              style="font-size: 25px; vertical-align: middle"
              @click="removeChapterThroughTime(k1)"
            ></i>
          </li>
        </ul>
      </li>
      <li style="height: auto">
        <div class="itemname">章节掉落：</div>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addChapter"
        ></i>
        <el-collapse>
          <el-collapse-item v-for="(v1, k1) in itemData.Chapters" :key="k1">
            <template class="itemname" slot="title">
              章节{{ k1 + 1 }}
              <i
                class="el-icon-plus"
                style="font-size: 25px; vertical-align: middle"
                @click="addChapterFall(k1)"
              ></i>
              <i
                class="el-icon-delete"
                style="font-size: 25px; vertical-align: middle"
                @click="removeChapter(k1)"
              ></i>
            </template>
            <ul>
              <li
                v-for="(v2, k2) in v1"
                :key="k2"
                class="itemname"
                slot="title"
                style="height: auto"
              >
                <span>掉落物{{ k2 + 1 }}：</span>
                <i
                  class="el-icon-delete"
                  style="font-size: 25px; vertical-align: middle"
                  @click="removeChapterFall(k1, k2)"
                ></i>
                <ul>
                  <li>
                    <span class="enemyK">掉落物ID：</span>
                    <el-input
                      v-model="v2.fallID"
                      placeholder="请输入掉落物ID"
                      style="width: 50%"
                    ></el-input>
                  </li>
                  <li>
                    <span class="enemyK">掉落概率：</span>
                    <el-input-number
                      class="enemyV"
                      v-model="v2.fallProbability"
                      style="width: 50%"
                    ></el-input-number>
                  </li>
                  <li>
                    <span class="enemyK">最小掉落数量：</span>
                    <el-input-number
                      class="enemyV"
                      v-model="v2.minFallNumber"
                      style="width: 50%"
                    ></el-input-number>
                  </li>
                  <li>
                    <span class="enemyK">最大掉落数量：</span>
                    <el-input-number
                      class="enemyV"
                      v-model="v2.maxFallNumber"
                      style="width: 50%"
                    ></el-input-number>
                  </li>
                </ul>
              </li>
            </ul>
          </el-collapse-item>
        </el-collapse>
      </li>
    </ul>
  </el-container>
</template>
<style>
.levelcontainer {
  list-style: none;
  float: left;
  text-align: left;
  padding-left: 0;
  width: 100%;
  vertical-align: middle;
}
.levelcontainer li {
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 5px 0;
}
.itemname {
  width: 150px;
  float: left;
}
.itemvalue {
  float: left;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.enemyK {
  width: 150px;
  float: left;
  height: 40px;
  overflow: auto;
}
.enemyV {
  width: 100px;
  float: left;
}
.enemyLine {
  width: 100%;
  height: 40px;
  line-height: 40px;
}
</style>
<script>
import Vue from "vue";
import globaldata from "../GlobalData.js";
export default {
  data() {
    return {
      formKey: "commonFalling",
      curItem: "",
      itemData: {},
      commonFallingconfigs: [
        { name: "关卡金币初始：", config: "initialLevelGoldCoin" },
        { name: "关卡金币每关增量：", config: "levelGoldCoinAdd" },
        { name: "关卡经验初始：", config: "initialLevelExp" },
        { name: "关卡经验每关增量：", config: "levelExp" },
      ],
      fallingConfigs: [
        { name: "掉落物ID：", config: "fallID", type: "string" },
        { name: "掉落概率：", config: "fallProbability" },
        { name: "最小掉落数量：", config: "minFallNumber" },
        { name: "最大掉落数量：", config: "MaxFallNumber" },
      ],
      chapterNumber: 0,
    };
  },
  components: {},
  mounted: function () {
    this.itemData = global.gamedata[this.formKey];
    console.log(this.itemData);
    if (!this.itemData) {
      var v = {};
      for (let k in this.commonFallingconfigs) {
        if (this.commonFallingconfigs[k]["length"]) {
          let n = [];
          for (let i = 0; i < this.commonFallingconfigs[k]["length"]; i++) {
            n.push(0);
          }
          v[this.commonFallingconfigs[k]["config"]] = n;
        } else {
          v[this.commonFallingconfigs[k]["config"]] = 0;
        }
      }
      Vue.set(global.gamedata, this.formKey, v);
      this.itemData = global.gamedata[this.formKey];
      //   if(this.itemData.Chapters){
      //       this.chapterNumber=this.itemData.Chapters.length;
      //   }
    }
    if (!this.itemData.equipSellPrice) {
      let evertyLevel = { green: 0, blue: 0, violet: 0, orange: 0, red: 0 };
      Vue.set(this.itemData, "equipSellPrice", evertyLevel);
    }
    if (!this.itemData.crystalSellPrice) {
      let evertyLevel = { blue: 0, violet: 0, orange: 0, red: 0 };
      Vue.set(this.itemData, "crystalSellPrice", evertyLevel);
    }
    if(!this.itemData.chapterThroughTime){
       Vue.set(this.itemData,"chapterThroughTime",[]);
    }
  },
  methods: {
    addChapter: function () {
      this.chapterNumber++;
      let chapter = [
        { fallID: "", fallProbability: 0, minFallNumber: 0, maxFallNumber: 0 },
      ];
      if (!this.itemData.Chapters || this.itemData.Chapters.length == 0) {
        Vue.set(this.itemData, "Chapters", [chapter]);
      } else {
        this.itemData.Chapters.push(chapter);
      }
    },
    removeChapter: function (idx) {
      this.itemData.Chapters.splice(idx, 1);
    },
    addChapterFall: function (index) {
      let chapterFall = {
        fallID: "",
        fallProbability: 0,
        minFallNumber: 0,
        maxFallNumber: 0,
      };
      this.itemData.Chapters[index].push(chapterFall);
    },
    removeChapterFall: function (index1, index2) {
      this.itemData.Chapters[index1].splice(index2, 1);
    },
    addChapterThroughTime:function(){
      this.itemData.chapterThroughTime.push(0);
    },
    removeChapterThroughTime:function(idx){
      this.itemData.chapterThroughTime.splice(idx,1);
    },
    forceUpdate: function () {
      this.$forceUpdate();
    },
    changes: function () {
      console.log("this.itemData.Chapters", this.itemData.Chapters);
    },
  },
};
</script>