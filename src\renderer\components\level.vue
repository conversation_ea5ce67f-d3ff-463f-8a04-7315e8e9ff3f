<template>
  <mylistview :formKey="formKey" v-on:setItemData="getItemData">
    <ul class="levelcontainer">
      <li>
        <div class="itemname">关卡名称:</div>
        <div class="itemvalue">{{ itemData.name }}</div>
      </li>
      <li>
        <div class="itemname">天空盒:</div>
        <!-- <span>{{ itemData.skybox }}</span> -->
        <el-dropdown class="itemvalue" @command="selectSkyBox">
          <span class="el-dropdown-link">
            {{ skyboxstr }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(value, idx) in skyboxtype"
              :command="idx" :key="idx"
              >{{ value }}</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </li>
      <li>
        <div class="itemname">使用地图:</div>
        <el-input
          class="itemvalue"
          v-model="itemData.maps"
          placeholder="请输入内容"
          style="width: 50%"
        ></el-input>
        <!-- <el-dropdown class="itemvalue" @command="selectMap">
          <span class="el-dropdown-link">
            {{ mapstr }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(value, idx) in maps" :command="idx">{{
              value
            }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown> -->
      </li>
      <li>
        <div class="itemname">怪物波次:</div>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addWave1"
        ></i>

        <!-- <el-tree
          :data="itemData.MapWaves"
          :props="waveProps"
          @node-click="handleNodeClick"
          :render-content="renderContent"
        ></el-tree> -->

        <el-collapse>
          <el-collapse-item v-for="(v1, k1) in itemData.MapWaves" :key="k1">
            <template class="itemname" slot="title">
              地图：
              <!-- <i class="el-icon-more"></i> -->
              <el-input
                v-model="v1.map"
                placeholder="请输入地图名称"
                style="width: 50%"
              ></el-input>
              <i
                class="el-icon-delete"
                style="font-size: 25px; vertical-align: middle"
                @click="removeWave1(k1)"
              ></i>
              <i
                class="el-icon-circle-plus-outline"
                style="font-size: 25px; vertical-align: middle"
                @click="addWave2(k1)"
              ></i>
            </template>
            <el-collapse style="margin-left: 50px">
              <el-collapse-item v-for="(v2, k2) in v1.AreaWaves" :key="k2">
                <template class="itemname" slot="title">
                  区域：
                  <el-input
                    v-model="v2.area"
                    placeholder="请输入区域名称"
                    style="width: 50%"
                  ></el-input>
                  <i
                    class="el-icon-delete"
                    style="font-size: 25px; vertical-align: middle"
                    @click="removeWave2(k1, k2)"
                  ></i>
                  <i
                    class="el-icon-circle-plus-outline"
                    style="font-size: 25px; vertical-align: middle"
                    @click="addWave3(k1, k2)"
                  ></i>
                </template>
                <el-collapse style="margin-left: 50px">
                  <el-collapse-item v-for="(v3, k3) in v2.waves" :key="k3">
                    <template class="itemname" slot="title">
                      第{{ k3 + 1 }}波：
                      <!-- <el-input
                        v-model="v2.area"
                        placeholder="请输入区域名称"
                        style="width: 50%"
                      ></el-input> -->
                      <i
                        class="el-icon-delete"
                        style="font-size: 25px; vertical-align: middle"
                        @click="removeWave3(k1, k2, k3)"
                      ></i>
                      <i
                        class="el-icon-circle-plus-outline"
                        style="font-size: 25px; vertical-align: middle"
                        @click="addWave4(k1, k2, k3)"
                      ></i>
                    </template>
                    <ul>
                      <li
                        style="width: 100%; float: left; height: auto"
                        v-for="(v4, k4) in v3" :key="k4"
                      >
                        <div class="enemyLine">
                          <span class="enemyK">怪物名称:</span>
                          <!-- <span class="enemyK">{{v3}}</span> -->
                          <el-input
                            class="enemyV"
                            v-model="v4.enemy"
                            placeholder="请输入怪物名称"
                            style="width: 50%"
                          ></el-input>
                          <i
                            class="el-icon-delete"
                            style="font-size: 25px; vertical-align: middle"
                            @click="removeWave4(k1, k2, k3, k4)"
                          ></i>
                        </div>
                        <div class="enemyLine">
                          <span class="enemyK">怪物数量:</span>
                          <el-input-number
                            class="enemyV"
                            v-model="v4.count"
                            :min="1"
                            :max="100"
                            label="怪物数量"
                          ></el-input-number>
                        </div>
                        <div class="enemyLine">
                          <span class="enemyK">怪物等级:</span>
                          <el-input-number
                            class="enemyV"
                            v-model="v4.level"
                            :min="0"
                            label="怪物等级"
                          ></el-input-number>
                        </div>
                        <div class="enemyLine">
                          <span class="enemyK">怪物出生延迟:</span>
                          <el-input
                            class="enemyV"
                            type="number"
                            v-model="v4.delay"
                            placeholder="请输入数值"
                            style="width: 50%"
                          ></el-input>
                        </div>
                        <div class="enemyLine">
                          <span class="enemyK" style="width: 150px"
                            >是否在UI上展示大血条:</span
                          >
                          <el-checkbox
                            class="enemyV"
                            v-model="v4.uiBlood"
                          ></el-checkbox>
                        </div>
                        <div class="enemyLine">
                          <span class="enemyK">怪物类型:</span>
                          <el-radio-group
                            class="itemvalue"
                            style="line-height: 40px"
                            v-model="v4.type"
                          >
                            <el-radio :label="0">普通怪</el-radio>
                            <el-radio :label="1">BOSS</el-radio>
                          </el-radio-group>
                        </div>
                        <div class="enemyLine" v-if="v4.type==1">
                          <span class="enemyK">Boss攻击增幅:</span>
                          <el-input
                            class="enemyV"
                            type="number"
                            v-model="v4.BossIncrease_Att"
                            placeholder="请输入数值"
                            style="width: 50%"
                          ></el-input>
                        </div>
                        <div class="enemyLine" v-if="v4.type==1">
                          <span class="enemyK">Boss血量增幅:</span>
                          <el-input
                            class="enemyV"
                            type="number"
                            v-model="v4.BossIncrease_Hp"
                            placeholder="请输入数值"
                            style="width: 50%"
                          ></el-input>
                        </div>
                      </li>
                    </ul>
                  </el-collapse-item>
                </el-collapse>
              </el-collapse-item>
            </el-collapse>
          </el-collapse-item>
        </el-collapse>
      </li>
    </ul>
  </mylistview>
</template>

<style>
.levelcontainer {
  list-style: none;
  float: left;
  text-align: left;
  padding-left: 0;
  width: 100%;
  vertical-align: middle;
}
.levelcontainer li {
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 5px 0;
}
.itemname {
  width: 150px;
  float: left;
}
.itemvalue {
  float: left;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.enemyK {
  width: 200px;
  float: left;
  height: 40px;
  overflow: auto;
}
.enemyV {
  width: 100px;
  float: left;
}
.enemyLine {
  width: 100%;
  height: 40px;
  line-height: 40px;
}
</style>
<script>
import mylistview from "./MyListView.vue";
import Vue from "vue";
const skyboxtype = ["系统", "白天", "黑夜", "蓝色", "红色"];
// const maps = ["1-1", "1-2", "1-3"];
export default {
  data() {
    return {
      formKey: "level",
      curItem: "",
      itemData: {},
      skyboxtype: skyboxtype,
      skyboxstr: "系统",
      waveProps: {
        children: "AreaWaves",
        label: "map",
        // label: (data,node)=>{
        //   console.log("--------------",node.level,data)
        //   return "map"
        //   // if(node.level===1){
        //   //   return data.map
        //   // }else if(node.level===2){
        //   //   return data.area
        //   // }else if(node.level===3){
        //   //   return data.enemys
        //   // }
        // },
      },
      // mapstr: "",
      // maps: maps,
    };
  },
  mounted: function () {
    console.log("mounted", this);
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      this.itemData = data;
      console.log("getItemData", data);
      if (this.itemData) {
        this.skyboxstr = skyboxtype[this.itemData.skybox] || "系统";
      } else {
        this.itemData = {};
      }
    },
    selectSkyBox: function (command) {
      // console.log("click on item " + command);
      this.itemData.skybox = parseInt(command);
      this.skyboxstr = skyboxtype[command];
    },
    addWave1: function () {
      // console.log("addWave1");
      var mapwave = { map: "", AreaWaves: [] };
      if (!this.itemData.MapWaves || this.itemData.MapWaves.length == 0) {
        Vue.set(this.itemData, "MapWaves", [mapwave]);
      } else {
        this.itemData.MapWaves.push(mapwave);
      }
    },
    removeWave1: function (idx) {
      // console.log(idx, this.itemData.MapWaves[idx]);
      var mapname = this.itemData.MapWaves[idx].map;
      this.$confirm("是否删除地图 " + mapname + " 的全部怪物配置?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.itemData.MapWaves.splice(idx, 1);
      });
    },
    addWave2: function (idx) {
      // console.log(idx, this.itemData.MapWaves[idx]);
      var areawave = { area: "", waves: [] };
      // this.itemData.MapWaves[idx].AreaWaves.push(areawave);
      if (
        !this.itemData.MapWaves[idx].AreaWaves ||
        this.itemData.MapWaves[idx].AreaWaves.length == 0
      ) {
        // this.itemData.MapWaves[idx].AreaWaves = [areawave];
        Vue.set(this.itemData.MapWaves[idx], "AreaWaves", [areawave]);
      } else {
        this.itemData.MapWaves[idx].AreaWaves.push(areawave);
      }
    },
    removeWave2: function (mapid, areaid) {
      // console.log("removeAreaWave",mapid,areaid,this.itemData.MapWaves[mapid].AreaWaves[areaid]);
      var mapname = this.itemData.MapWaves[mapid].map;
      var areaname = this.itemData.MapWaves[mapid].AreaWaves[areaid].area;
      this.$confirm(
        "是否删除地图" + mapname + "内区域 " + areaname + " 的全部怪物配置?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        // console.log("removeAreaWave", mapname, areaname);
        this.itemData.MapWaves[mapid].AreaWaves.splice(areaid, 1);
        console.log(this.itemData.MapWaves[mapid].AreaWaves);
      });
    },
    addWave3: function (mapid, areaid) {
      // console.log(
      //   mapid,
      //   areaid,
      //   this.itemData.MapWaves[mapid].AreaWaves[areaid]
      // );
      var wave = [];
      // this.itemData.MapWaves[idx].AreaWaves.push(areawave);
      if (
        !this.itemData.MapWaves[mapid].AreaWaves[areaid].waves ||
        this.itemData.MapWaves[mapid].AreaWaves[areaid].waves.length == 0
      ) {
        // this.itemData.MapWaves[mapid].AreaWaves[areaid].waves = [wave];
        Vue.set(this.itemData.MapWaves[mapid].AreaWaves[areaid], "waves", [
          wave,
        ]);
      } else {
        this.itemData.MapWaves[mapid].AreaWaves[areaid].waves.push(wave);
      }
    },
    removeWave3: function (mapid, areaid, waveid) {
      // console.log("removeAreaWave",mapid,areaid,this.itemData.MapWaves[mapid].AreaWaves[areaid]);
      // var mapname = this.itemData.MapWaves[mapid].map;
      // var areaname = this.itemData.MapWaves[mapid].AreaWaves[areaid].area;
      this.$confirm("是否删除这一波的怪物配置?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        // console.log("removeAreaWave", mapname, areaname);
        this.itemData.MapWaves[mapid].AreaWaves[areaid].waves.splice(waveid, 1);
        // console.log(this.itemData.MapWaves[mapid].AreaWaves);
      });
    },
    addWave4: function (mapid, areaid, waveid) {
      // console.log("addWave1");
      var enemy = {
        enemy: "",
        // pos: "",
        count: 0,
        level: 0,
        delay: 0,
        uiBlood: false,
        type: 0,
        BossIncrease_Att:1,
        BossIncrease_Hp:1
      };
      if (
        !this.itemData.MapWaves[mapid].AreaWaves[areaid].waves[waveid] ||
        this.itemData.MapWaves[mapid].AreaWaves[areaid].waves[waveid].length ==
          0
      ) {
        // this.itemData.MapWaves[mapid].AreaWaves[areaid].waves[waveid] = [enemy];
        Vue.set(
          this.itemData.MapWaves[mapid].AreaWaves[areaid].waves[waveid],
          0,
          enemy
        );
      } else {
        this.itemData.MapWaves[mapid].AreaWaves[areaid].waves[waveid].push(
          enemy
        );
      }
    },
    removeWave4: function (mapid, areaid, waveid, idx) {
      this.$confirm("是否删除这坨怪的配置?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.itemData.MapWaves[mapid].AreaWaves[areaid].waves[waveid].splice(
          idx,
          1
        );
      });
    },
    selectMap: function (command) {
      console.log(command);
      //   this.itemData.map = maps[command];
      //   this.mapstr = maps[command];
    },
    handleNodeClick: function (data) {
      console.log(data);
    },
    renderContent: function (h, { node, data }) {
      console.log(node, data);
      return (
        <li>
          <div class="itemname">地图： </div>
          <el-input
            class="itemvalue"
            v-model="data.map"
            placeholder="请输入内容"
            style="width: 50%"
          ></el-input>
        </li>
      );
    },
  },
};
</script>