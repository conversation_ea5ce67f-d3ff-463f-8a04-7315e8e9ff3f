<style>
.activitycontainer {
    list-style: none;
    padding-left: 0;
    width: 100%;
    overflow: hidden;
}

.activitycontainer li {
    width: 100%;
    min-height: 40px;
    margin: 5px 0;
    clear: both;
}

.itemname {
    width: 150px;
    float: left;
    line-height: 40px;
}

.itemvalue {
    width: 50%;
    float: left;
}

/* 礼包项样式 */
.gift-item {
    border: 1px solid #eee;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    width: 100%;
}

/* 表单项样式 */
.form-row {
    display: flex;
    align-items: center;
    margin: 10px 0;
}

.form-label {
    width: 80px;
    flex-shrink: 0;
}

/* 奖励项样式 */
.reward-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 5px 0;
}

/* 操作按钮样式 */
.operation-btn {
    margin-left: 10px;
    cursor: pointer;
}
</style>

<template>
    <mylistview :formKey="formKey" @setItemData="getItemData">
        <div class="activitycontainer">
            <!-- 基本信息 -->
            <div class="form-row">
                <div class="itemname">名称:</div>
                <el-input class="itemvalue" v-model="itemData.name" placeholder="请输入活动礼包名称"></el-input>
            </div>

            <div class="form-row">
                <div class="itemname">描述:</div>
                <el-input class="itemvalue" type="textarea" v-model="itemData.description"
                    placeholder="请输入活动礼包描述"></el-input>
            </div>

            <!-- 七日签到 -->
            <div class="gift-item">
                <div class="form-row">
                    <h3 style="margin: 0;">七日签到奖励</h3>
                </div>
                <div v-for="(reward, dayIndex) in itemData.signin" :key="dayIndex" class="reward-item">
                    <div class="form-label">第{{dayIndex + 1}}天：</div>
                    <el-select v-model="reward.id" clearable filterable allow-create placeholder="奖励ID"
                        class="product-input" style="width: 200px;">
                        <el-option v-for="(item, idx) in CommonlyArticles" :key="idx" :label="getArticleName(item)"
                            :value="item" />
                    </el-select>
                    <el-input type="number" v-model.number="reward.count" placeholder="数量" style="width: 100px">
                    </el-input>
                    <span>{{getArticleName(itemData.signin[dayIndex].id)}}</span>
                </div>
            </div>

            <!-- 礼包列表 -->
            <div v-for="(gift, index) in itemData.packages" :key="index" class="gift-item">
                <div class="form-row">
                    <h3 style="margin: 0;">{{gift.name}}</h3>
                </div>

                <div class="form-row">
                    <div class="form-label">计费ID：</div>
                    <el-input v-model="gift.payID" placeholder="请输入计费ID" style="width: 200px">
                    </el-input>
                </div>

                <div class="form-row">
                    <span style="margin: 0 10px;">限购次数(0为不限)：</span>
                    <el-input type="number" v-model.number="gift.purchaseLimit" placeholder="请输入购买次数限制" style="width: 100px">
                    </el-input>
                </div>

                <div class="form-row">
                    <div class="form-label">奖励内容：</div>
                    <i class="el-icon-plus operation-btn" @click="addPackageReward(index)"></i>
                </div>

                <div v-for="(reward, rIndex) in gift.rewards" :key="rIndex" class="reward-item">
                    <el-select v-model="reward.id" clearable filterable allow-create placeholder="奖励ID"
                        class="product-input" style="width: 200px;">
                        <el-option v-for="(item, idx) in CommonlyArticles" :key="idx" :label="getArticleName(item)"
                            :value="item" />
                    </el-select>
                    <el-input type="number" v-model.number="reward.count" placeholder="数量" style="width: 100px">
                    </el-input>
                    <i class="el-icon-delete operation-btn" @click="removePackageReward(index, rIndex)"></i>
                    <span>{{getArticleName(reward.id)}}</span>
                </div>
            </div>

            <!-- 礼包列表 -->
            <!-- 捆绑礼包 -->
            <div class="gift-item">
                <div class="form-row">
                    <h3 style="margin: 0;">{{itemData.bundle.name}}</h3>
                </div>

                <div class="form-row">
                    <div class="form-label">计费ID：</div>
                    <el-input v-model="itemData.bundle.payID" placeholder="请输入计费ID" style="width: 200px">
                    </el-input>
                </div>

                <div class="form-row">
                    <span style="margin: 0 10px;">限购次数(0为不限)：</span>
                    <el-input type="number" v-model.number="itemData.bundle.purchaseLimit" placeholder="请输入购买次数限制" style="width: 100px">
                    </el-input>
                </div>

                <div class="form-row">
                    <div class="form-label">奖励内容：</div>
                    <i class="el-icon-plus operation-btn" @click="addPackageReward('bundle')"></i>
                </div>

                <div v-for="(reward, rIndex) in itemData.bundle.rewards" :key="rIndex" class="reward-item">
                    <el-select v-model="reward.id" clearable filterable allow-create placeholder="奖励ID"
                        class="product-input" style="width: 200px;">
                        <el-option v-for="(item, idx) in CommonlyArticles" :key="idx" :label="getArticleName(item)"
                            :value="item" />
                    </el-select>
                    <el-input type="number" v-model.number="reward.count" placeholder="数量" style="width: 100px">
                    </el-input>
                    <i class="el-icon-delete operation-btn" @click="removePackageReward('bundle', rIndex)"></i>
                    <span>{{getArticleName(reward.id)}}</span>
                </div>
            </div>

            <!-- 小额道具礼包 -->
            <div class="gift-item">
                <div class="form-row">
                    <h3 style="margin: 0;">{{itemData.smallPropPack.name}}</h3>
                </div>

                <div class="form-row">
                    <div class="form-label">计费ID：</div>
                    <el-input v-model="itemData.smallPropPack.payID" placeholder="请输入计费ID" style="width: 200px">
                    </el-input>
                </div>

                <div class="form-row">
                    <span style="margin: 0 10px;">限购次数(0为不限)：</span>
                    <el-input type="number" v-model.number="itemData.smallPropPack.purchaseLimit" placeholder="请输入购买次数限制" style="width: 100px">
                    </el-input>
                </div>

                <div class="form-row">
                    <div class="form-label">奖励内容：</div>
                    <i class="el-icon-plus operation-btn" @click="addPackageReward('smallPropPack')"></i>
                </div>

                <div v-for="(reward, rIndex) in itemData.smallPropPack.rewards" :key="rIndex" class="reward-item">
                    <el-select v-model="reward.id" clearable filterable allow-create placeholder="奖励ID"
                        class="product-input" style="width: 200px;">
                        <el-option v-for="(item, idx) in CommonlyArticles" :key="idx" :label="getArticleName(item)"
                            :value="item" />
                    </el-select>
                    <el-input type="number" v-model.number="reward.count" placeholder="数量" style="width: 100px">
                    </el-input>
                    <i class="el-icon-delete operation-btn" @click="removePackageReward('smallPropPack', rIndex)"></i>
                    <span>{{getArticleName(reward.id)}}</span>
                </div>
            </div>

            <!-- 小额特惠礼包 -->
            <div class="gift-item">
                <div class="form-row">
                    <h3 style="margin: 0;">{{itemData.smallDiscountPackage.name}}</h3>
                </div>

                <div class="form-row">
                    <div class="form-label">计费ID：</div>
                    <el-input v-model="itemData.smallDiscountPackage.payID" placeholder="请输入计费ID" style="width: 200px">
                    </el-input>
                </div>

                <div class="form-row">
                    <span style="margin: 0 10px;">限购次数(0为不限)：</span>
                    <el-input type="number" v-model.number="itemData.smallDiscountPackage.purchaseLimit" placeholder="请输入购买次数限制" style="width: 100px">
                    </el-input>
                </div>

                <div class="form-row">
                    <div class="form-label">奖励内容：</div>
                    <i class="el-icon-plus operation-btn" @click="addPackageReward('smallDiscountPackage')"></i>
                </div>

                <div v-for="(reward, rIndex) in itemData.smallDiscountPackage.rewards" :key="rIndex" class="reward-item">
                    <el-select v-model="reward.id" clearable filterable allow-create placeholder="奖励ID"
                        class="product-input" style="width: 200px;">
                        <el-option v-for="(item, idx) in CommonlyArticles" :key="idx" :label="getArticleName(item)"
                            :value="item" />
                    </el-select>
                    <el-input type="number" v-model.number="reward.count" placeholder="数量" style="width: 100px">
                    </el-input>
                    <i class="el-icon-delete operation-btn" @click="removePackageReward('smallDiscountPackage', rIndex)"></i>
                    <span>{{getArticleName(reward.id)}}</span>
                </div>
            </div>
        </div>
    </mylistview>
</template>

<script>
import Vue from "vue";
import mylistview from "./MyListView.vue";

export default {
    data() {
        return {
            formKey: "holidayActivity",
            CommonlyArticles: [],
            itemData: {
                name: "",
                description: "",
                signin: Array(7).fill(null).map(() => ({
                    id: "",
                    count: 1
                })),
                bundle: {
                    name: "捆绑礼包",
                    payID: "",
                    purchaseLimit: 1,
                    rewards: []
                },
                smallPropPack: {
                    name: "小额道具礼包",
                    payID: "",
                    purchaseLimit: 1,
                    rewards: []
                },
                smallDiscountPackage: {
                    name: "小额优惠礼包",
                    payID: "",
                    purchaseLimit: 1,
                    rewards: []
                }
            }
        };
    },
    components: {
        mylistview,
    },
    methods: {
        getItemData(data) {
            if (data) {
                // 删除可能存在的price字段
                if (data.bundle && data.bundle.price) delete data.bundle.price;
                if (data.smallPropPack && data.smallPropPack.price) delete data.smallPropPack.price;
                if (data.smallDiscountPackage && data.smallDiscountPackage.price) delete data.smallDiscountPackage.price;
                this.itemData = data;
            }
            this.CommonlyArticles = global.gamedata["CommonlyArticles"] || [];
            
            // 确保signin数组存在且长度为7
            if (!this.itemData.signin) {
                Vue.set(this.itemData, "signin", Array(7).fill(null).map(() => ({
                    id: "",
                    count: 1
                })));
            }
            
            // 确保礼包属性存在
            if (!this.itemData.bundle) {
                Vue.set(this.itemData, "bundle", {
                    name: "捆绑礼包",
                    payID: "",
                    purchaseLimit: 1,
                    rewards: []
                });
            }
            
            if (!this.itemData.smallPropPack) {
                Vue.set(this.itemData, "smallPropPack", {
                    name: "小额道具礼包",
                    payID: "",
                    purchaseLimit: 1,
                    rewards: []
                });
            }
            
            if (!this.itemData.smallDiscountPackage) {
                Vue.set(this.itemData, "smallDiscountPackage", {
                    name: "小额优惠礼包",
                    payID: "",
                    purchaseLimit: 1,
                    rewards: []
                });
            }
        },
        addPackageReward(packageType) {
            const reward = {
                id: "",
                count: 1
            };
            this.itemData[packageType].rewards.push(reward);
        },
        removePackageReward(packageType, rewardIndex) {
            this.itemData[packageType].rewards.splice(rewardIndex, 1);
        },
        handleRewardInput(value, reward) {
            if (value && !this.CommonlyArticles.includes(value)) {
                reward.id = value;
            }
        },
        getArticleName(articleId) {
            let _article = global.gamedata["article"][articleId];
            if (!_article) return "";
            return _article.name;
        }
    },
};
</script>