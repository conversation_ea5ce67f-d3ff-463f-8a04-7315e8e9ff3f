<template>
  <mylistview :formKey="formKey" v-on:setItemData="getItemData">
    <ul class="levelcontainer">
      <li>
        <div class="enemyLine">
          <span class="enemyK">名字:</span>
          <el-input
            class="enemyV"
            v-model="itemData.name"
            placeholder="请输入装备名称"
            style="width: 50%"
          ></el-input>
        </div>
      </li>
      <li>
        <div class="enemyLine">
          <span class="enemyK">描述:</span>
          <el-input
            class="enemyV"
            v-model="itemData.desc"
            placeholder="请输入装备描述"
            style="width: 50%"
          ></el-input>
        </div>
      </li>
      <li>
        <div class="itemname">品质:</div>
        <el-dropdown class="itemvalue" @command="selectQualityBox">
          <span class="el-dropdown-link">
            {{ qualityStr }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(value, idx) in qualityType"
              :command="idx" :key="idx"
              >{{ value }}</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </li>
      <li>
        <div class="itemname">部位:</div>
        <el-dropdown class="itemvalue" @command="selectPartBox">
          <span class="el-dropdown-link">
            {{ partStr }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(value, idx) in partType" :command="idx" :key="idx">{{
              value
            }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </li>
      <li v-for="(value, key) in equipconfigs" :key="key">
        <div class="enemyLine">
          <span class="enemyK">{{ value.name }}</span>
          <el-input
            v-if="value.type === 'string'"
            class="enemyV"
            v-model="itemData[value.config]"
            style="width: 50%"
          ></el-input>
          <el-input
            v-else
            class="enemyV"
            type="number"
            v-model.number="itemData[value.config]"
            style="width: 50%"
          ></el-input>
        </div>
      </li>
    </ul>
  </mylistview>
</template>
<script>
import mylistview from "./MyListView.vue";
import Vue from "vue";
const qualityType = ["绿色", "蓝色", "紫色", "金色", "红色"];
const partType = ["武器", "铠甲", "副手", "披风"];
export default {
  data() {
    return {
      formKey: "equip",
      curItem: "",
      itemData: {},
      qualityStr: "绿色",
      qualityType: qualityType,
      partStr: "武器",
      partType: partType,
      equipconfigs: [
        { name: "基础血量", config: "hp" },
        { name: "血量增量", config: "addhp" },
        // { name: "基础蓝量", config: "mp" },
        // { name: "蓝量增量", config: "addmp" },
        { name: "基础攻击力", config: "attack" },
        { name: "攻击力增量", config: "addattack" },
        { name: "基础防御", config: "def" },
        { name: "防御增量", config: "adddef" },
        // { name: "基础命中", config: "hit" },
        // { name: "命中增量", config: "addhit" },
        { name: "基础闪避", config: "dodge" },
        { name: "闪避增量", config: "adddodge" },
        { name: "基础暴击", config: "crit" },
        { name: "暴击增量", config: "addcrit" },
        { name: "基础穿刺", config: "puncture" },
        { name: "穿刺增量", config: "addpuncture" },
        { name: "基础冷却", config: "cd" },
        { name: "冷却缩减", config: "reducecd" },
        // { name: "基础速度", config: "speed" },
        // { name: "速度增量", config: "addspeed" },
        // { name: "每秒回血", config: "hp1s" },
        // { name: "回血增量", config: "addhp1s" },
        // { name: "每秒回蓝", config: "mp1s" },
        // { name: "回蓝增量", config: "addmp1s" },
      ],
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      this.itemData = data;
      console.log("getItemData", data);
      if (this.itemData) {
        this.qualityStr = qualityType[this.itemData.quality] || "绿色";
        this.partStr = partType[this.itemData.part] || "武器";
      } else {
        this.itemData = {};
      }
    },
    selectQualityBox: function (command) {
      // console.log("click on item " + command);
      this.itemData.quality = parseInt(command);
      this.qualityStr = qualityType[command];
    },
    selectPartBox: function (command) {
      this.itemData.part = parseInt(command);
      this.partStr = partType[command];
    },
  },
};
</script>