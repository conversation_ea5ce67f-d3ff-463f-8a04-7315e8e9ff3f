<template>
  <el-container :formKey="formKey">
    <ul class="levelcontainer">
      <li v-for="(value, key) in commonRankconfigs" :key="key">
        <div class="" v-if="value.length">
          <span class="enemyLine" style="float: left">{{ value.name }}</span>
          <div
            class="enemyLine"
            style="float: left"
            v-for="idx in value.length"
            :key="idx"
          >
            <span class="enemyK">{{ idx }}</span>
            <el-input
              v-if="value.type === 'string'"
              class="enemyV"
              v-model="itemData[value.config][idx - 1]"
              style="width: 50%"
            ></el-input>

            <el-input-number
              v-else
              class="enemyV"
              v-model.number="itemData[value.config][idx - 1]"
              style="width: 50%"
            ></el-input-number>
          </div>
        </div>
        <div class="enemyLine" style="float: left" v-else>
          <span class="enemyK">{{ value.name }}</span>
          <el-input
            v-if="value.type === 'string'"
            class="enemyV"
            v-model="itemData[value.config]"
            style="width: 50%"
          ></el-input>

          <el-input-number
            v-else
            class="enemyV"
            v-model.number="itemData[value.config]"
            style="width: 50%"
          ></el-input-number>
        </div>
      </li>
      <li style="height: auto">
        <div class="itemname">关卡掉落：</div>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addOuBuLevel"
        ></i>
        <el-collapse>
          <el-collapse-item v-for="(v1, k1) in itemData.ouBuLevels" :key="k1">
            <template class="itemname" slot="title">
              关卡{{ k1 + 1 }}
              <i
                class="el-icon-plus"
                style="font-size: 25px; vertical-align: middle"
                @click="addOuBuLevelFall(k1)"
              ></i>
              <i
                class="el-icon-delete"
                style="font-size: 25px; vertical-align: middle"
                @click="removeOuBuLevel(k1)"
              ></i>
            </template>
            <ul>
              <li
                v-for="(v2, k2) in v1"
                :key="k2"
                class="itemname"
                slot="title"
                style="height: auto"
              >
                <span>掉落物{{ k2 + 1 }}：</span>
                <i
                  class="el-icon-delete"
                  style="font-size: 25px; vertical-align: middle"
                  @click="removeOuBuLevelFall(k1, k2)"
                ></i>
                <ul>
                  <li>
                    <span class="enemyK">掉落物ID：</span>
                    <el-input
                      v-model="v2.fallID"
                      placeholder="请输入掉落物ID"
                      style="width: 50%"
                    ></el-input>
                  </li>
                  <li>
                    <span class="enemyK">掉落概率：</span>
                    <el-input-number
                      class="enemyV"
                      v-model="v2.fallProbability"
                      style="width: 50%"
                    ></el-input-number>
                  </li>
                  <li>
                    <span class="enemyK">掉落数量：</span>
                    <el-input-number
                      class="enemyV"
                      v-model="v2.fallNums"
                      style="width: 50%"
                    ></el-input-number>
                  </li>
                </ul>
              </li>
            </ul>
          </el-collapse-item>
        </el-collapse>
      </li>
    </ul>
  </el-container>
</template>
<style>
.levelcontainer {
  list-style: none;
  float: left;
  text-align: left;
  padding-left: 0;
  width: 100%;
  vertical-align: middle;
}
.levelcontainer li {
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 5px 0;
}
.itemname {
  width: 150px;
  float: left;
}
.itemvalue {
  float: left;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.enemyK {
  width: 150px;
  float: left;
  height: 40px;
  overflow: auto;
}
.enemyV {
  width: 100px;
  float: left;
}
.enemyLine {
  width: 100%;
  height: 40px;
  line-height: 40px;
}
</style>
<script>
import Vue from "vue";
import globaldata from "../GlobalData.js";
export default {
  data() {
    return {
      formKey: "ouBuLevel",
      curItem: "",
      itemData: {},
      commonRankconfigs: [
        { name: "每日挑战次数", config: "dailyChallengeNums" },
        { name: "每日视频挑战次数", config: "dailyVideoChallengeNums" },
      ],
    };
  },
  components: {},
  mounted: function () {
    this.itemData = global.gamedata[this.formKey];
    console.log(this.itemData);
    if (!this.itemData) {
      var v = {};
      Vue.set(global.gamedata, this.formKey, v);
      this.itemData = global.gamedata[this.formKey];
    }
  },
  methods: {
      addOuBuLevel: function () {
      let ouBuLevel = [
        { fallID: "", fallProbability: 0, fallNums: 0},
      ];
      if (!this.itemData.ouBuLevels || this.itemData.ouBuLevels.length == 0) {
        Vue.set(this.itemData, "ouBuLevels", [ouBuLevel]);
      } else {
        this.itemData.ouBuLevels.push(ouBuLevel);
      }
    },
    removeOuBuLevel: function (idx) {
      this.itemData.ouBuLevels.splice(idx, 1);
    },
    addOuBuLevelFall: function (index) {
      let ouBuLevelFall = {
        fallID: "",
        fallProbability: 0,
        fallNums: 0
      };
      this.itemData.ouBuLevels[index].push(ouBuLevelFall);
    },
    removeOuBuLevelFall: function (index1, index2) {
      this.itemData.ouBuLevels[index1].splice(index2, 1);
    },
  },
};
</script>