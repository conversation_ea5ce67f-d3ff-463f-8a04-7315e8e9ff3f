const ele = require('electron');
// import { dialog } from 'electron'
const fs = require('fs');
// import db from './store/modules/DataBase'

// var leveldata = {
//     "1-1": { name: "1-1" },
//     "1-2": { name: "1-2" },
//     "1-3": { name: "1-3" },
// };
// var roledata = {
//     luobu: { name: "luobu" },
//     tuoleijiya: { name: "tuole<PERSON><PERSON>" },
// };
global.dbfilepath = ""
global.gamedata = {}
global.exportpath = null;

function hasData() {
    console.log("hasData", global.dbfilepath)
    if (global.dbfilepath == null || global.dbfilepath == "") {
        return false;
    }
    return true;
}

function getData(key) {
    console.log("getData", key);
    return global.gamedata[key] || {};
}

function setData(key, value) {
    console.log("setData", key, value);
    global.gamedata[key] = value;
}

function createDB() {
    ele.remote.dialog.showSaveDialog({
        title: "创建文件",
        buttonLabel: '创建',
        nameFieldLabel: '请输入要创建的文件名',
        defaultPath: "/",
        filters: [{ name: '存档文件', extensions: ['evd'] }]
    }, function(filename) {
        if (filename) {
            // console.log("create", filename)
            fs.writeFile(filename, "{}", 'utf-8', function(err) {
                if (err) {
                    console.log("创建存档文件失败", filename, err);
                    return;
                }
                global.dbfilepath = filename;
            })
        }
    })
}

function readDB() {
    ele.remote.dialog.showOpenDialog({
        title: "打开文件",
        defaultPath: "/",
        properties: ['openFile'],
        filters: [{ name: '存档文件', extensions: ['evd'] }]
    }, function(files) {
        if (files) {
            var filename = files[0];
            fs.readFile(filename, 'utf-8', function(err, data) {
                if (err) {
                    console.log("读取存档文件失败", filename)
                    return;
                }
                // db.dbfilepath = filename;
                global.dbfilepath = filename;
                global.gamedata = JSON.parse(data);
                console.log("read", data);
            })
        }
    })
}

function saveDB() {
    // console.log("sd", db)
    var self = this;
    // console.log("sv", self)
    return new Promise(function(resolve, reject) {
        // console.log("sa", db);
        if (!self.hasData()) {
            // if (global.dbfilepath == null || global.dbfilepath == "") {
            reject("未读取数据文件");
            return;
        }
        for (let r in global.gamedata.role) {
            if (global.gamedata.role[r].skills) {
                if (global.gamedata.role[r].skills.length > 5) {
                    for (let t = global.gamedata.role[r].skills.length; t > 5; t--) {
                        global.gamedata.role[r].skills.pop();
                    }
                }
            }
            if (global.gamedata.role[r].addpassive || global.gamedata.role[r].addpassive == "") {
                delete global.gamedata.role[r].addpassive
            }
            if (global.gamedata.role[r].passive || global.gamedata.role[r].passive == "") {
                delete global.gamedata.role[r].passive
            }
        }
        for (let l in global.gamedata.level) {
            if (global.gamedata.level[l].MapWaves) {
                for (let j = 0; j < global.gamedata.level[l].MapWaves.length; j++) {
                    if (global.gamedata.level[l].MapWaves[j].AreaWaves) {
                        for (let x = 0; x < global.gamedata.level[l].MapWaves[j].AreaWaves.length; x++) {
                            if (global.gamedata.level[l].MapWaves[j].AreaWaves[x].waves) {
                                for (let y = 0; y < global.gamedata.level[l].MapWaves[j].AreaWaves[x].waves.length; y++) {
                                    for (let z = 0; z < global.gamedata.level[l].MapWaves[j].AreaWaves[x].waves[y].length; z++) {
                                        if (global.gamedata.level[l].MapWaves[j].AreaWaves[x].waves[y][z]) {
                                            if (global.gamedata.level[l].MapWaves[j].AreaWaves[x].waves[y][z].type == 1) {
                                                // global.gamedata.level[l].MapWaves[j].AreaWaves[x].waves[y][z].BossIncrease_Att = global.gamedata.level[l].MapWaves[j].AreaWaves[x].waves[y][z].BossIncrease;
                                                // global.gamedata.level[l].MapWaves[j].AreaWaves[x].waves[y][z].BossIncrease_Hp = global.gamedata.level[l].MapWaves[j].AreaWaves[x].waves[y][z].BossIncrease;

                                                if (global.gamedata.levelDetails[l]) {
                                                    global.gamedata.levelDetails[l].bossName = [];
                                                    global.gamedata.levelDetails[l].bossName.push(global.gamedata.level[l].MapWaves[j].AreaWaves[x].waves[y][z].enemy);
                                                }
                                            }
                                            // delete global.gamedata.level[l].MapWaves[j].AreaWaves[x].waves[y][z].BossIncrease;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        console.log("global.gamedata", global.gamedata.commonFalling);
        fs.writeFile(global.dbfilepath, JSON.stringify(global.gamedata), 'utf-8', function(err) {
            if (err) {
                console.log("保存文件失败", filename, err);
                reject(JSON.stringify(err));
                return;
            }
            (new ele.remote.Notification({ title: "提示", body: "文件保存成功" })).show();
            resolve();
        })

    })
}

function saveDBto() {

}

function setExportPath() {
    ele.remote.dialog.showOpenDialog({
        title: "选择文件夹",
        defaultPath: "/",
        properties: ['openDirectory'],
        // filters: [{ name: '存档文件', extensions: ['evd'] }]
    }, function(dirs) {
        if (dirs) {
            var dir = dirs[0];
            global.exportpath = dir;
            console.log("设置导出路径为:", dir);
        }
    })
}

function exportAllData() {
    console.log("path", global.dbfilepath);
    console.log("data", global.gamedata);
    if (!this.hasData()) {
        (new ele.remote.Notification({ title: "提示", body: "未读取配置文件" })).show();
        return;
    }
    let path;
    if (global.exportpath) {
        path = exportpath;
    } else {
        path = global.dbfilepath.substr(0, global.dbfilepath.lastIndexOf(".evd"));
    }
    console.log("p", path)


    if (global.gamedata["role"]) {
        let filename = path + "/basedata/players.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["role"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["equip"]) {
        let filename = path + "/basedata/equipments.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["equip"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["article"]) {
        let filename = path + "/basedata/articles.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["article"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["crystal"]) {
        let filename = path + "/basedata/crystals.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["crystal"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["upgrade"]) {
        let filename = path + "/basedata/upgradedata.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["upgrade"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["levelDetails"]) {
        let filename = path + "/basedata/levelDetails.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["levelDetails"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["vipPrivilege"]) {
        let filename = path + "/basedata/vipPrivilege.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["vipPrivilege"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["crystalExplore"]) {
        let filename = path + "/basedata/crystalExplore.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["crystalExplore"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["plot"]) {
        let filename = path + "/basedata/plot.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["plot"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["guide"]) {
        let filename = path + "/basedata/guide.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["guide"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["commonFalling"]) {
        let filename = path + "/basedata/commonFalling.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["commonFalling"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["specialLevel"]) {
        let filename = path + "/basedata/specialLevel.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["specialLevel"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["propsPool"]) {
        let filename = path + "/basedata/propsPool.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["propsPool"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["shop"]) {
        let filename = path + "/basedata/shop.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["shop"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["achievement"]) {
        let filename = path + "/basedata/achievement.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["achievement"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["buff"]) {
        let filename = path + "/basedata/buff.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["buff"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }


    // for (let r in global.gamedata["role"]) {
    //     let filename = path + "/players/basedata/" + r + ".json";
    //     existsDist(filename, function() {
    //         fs.writeFile(filename, JSON.stringify(global.gamedata["role"][r]), "utf-8", function(err) {
    //             if (err) {
    //                 console.log("下载失败", err);
    //             } else {
    //                 // console.log("下载成功", filename, global.gamedata["role"][r]);
    //             }
    //         });
    //     })
    // }

    for (let l in global.gamedata["level"]) {
        let filename = path + "/levels/data/" + l + ".json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["level"][l]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["level"][l]);
                }
            });
        })
    }

    if (global.gamedata["advert"]) {
        let filename = path + "/basedata/advert.json";
        existsDist(filename, function() {
            let advs = global.gamedata["advert"];
            let outadvs = {};
            for (let k in advs) {
                let adv = advs[k];
                outadvs[k] = {};
                for (let idx in adv.advs) {
                    let a = adv.advs[idx];
                    outadvs[k][a.pos] = a.id;
                }
            }
            fs.writeFile(filename, JSON.stringify(outadvs), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["ranksPool"]) {
        let filename = path + "/basedata/ranksPool.json";
        let data=JSON.stringify(global.gamedata["ranksPool"]);
        let outData=JSON.parse(data);
        if(outData.RankPools){
            for(let i in outData.RankPools){
                let section;
                section=outData.RankPools[i].npcPlayerLevelSection.split("-");
                outData.RankPools[i].npcPlayerLevelSection=[Number(section[0]),Number(section[1])];

                section=outData.RankPools[i].npcPlayerEquipmentLevelSection.split("-");
                outData.RankPools[i].npcPlayerEquipmentLevelSection=[Number(section[0]),Number(section[1])];

                section=outData.RankPools[i].npcPlayerHeroSection.split("-");
                outData.RankPools[i].npcPlayerHeroSection=section;
                let dailyNpcPlayerRankNumSections=outData.RankPools[i].dailyNpcPlayerRankNumSectionDatas.dailyNpcPlayerRankNumSections;
                if(dailyNpcPlayerRankNumSections){
                    for(let j in dailyNpcPlayerRankNumSections){
                        section=dailyNpcPlayerRankNumSections[j].section.split("-");
                        dailyNpcPlayerRankNumSections[j].section=[Number(section[0]),Number(section[1])];
                    }
                }
                let dailyRankEndRewoards=outData.RankPools[i].dailyRankEndRewoards;
                if(dailyRankEndRewoards){
                    for(let z in dailyRankEndRewoards){
                        section=dailyRankEndRewoards[z].section.split("-");
                        dailyRankEndRewoards[z].section=[Number(section[0]),Number(section[1])];
                    }
                }
                
            }
        }
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(outData), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["roleSkin"]) {
        let filename = path + "/basedata/roleSkin.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["roleSkin"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["ouBuLevel"]) {
        let filename = path + "/basedata/ouBuLevel.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["ouBuLevel"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["battlePass"]) {
        let filename = path + "/basedata/battlePass.json";
        existsDist(filename, function() {
            fs.writeFile(filename, JSON.stringify(global.gamedata["battlePass"]), "utf-8", function(err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["newHeroGift"]) {
        let filename = path + "/basedata/newHeroGift.json";
        existsDist(filename, function () {
            fs.writeFile(filename, JSON.stringify(global.gamedata["newHeroGift"]), "utf-8", function (err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["chainedGift"]) {
        let filename = path + "/basedata/chainedGift.json";
        existsDist(filename, function () {
            fs.writeFile(filename, JSON.stringify(global.gamedata["chainedGift"]), "utf-8", function (err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["holidayActivity"]) {
        let filename = path + "/basedata/holidayActivity.json";
        existsDist(filename, function () {
            fs.writeFile(filename, JSON.stringify(global.gamedata["holidayActivity"]), "utf-8", function (err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["collectCards"]) {
        let filename = path + "/basedata/collectCards.json";
        existsDist(filename, function () {
            fs.writeFile(filename, JSON.stringify(global.gamedata["collectCards"]), "utf-8", function (err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    if (global.gamedata["worldBoss"]) {
        let filename = path + "/basedata/worldBoss.json";
        existsDist(filename, function () {
            fs.writeFile(filename, JSON.stringify(global.gamedata["worldBoss"]), "utf-8", function (err) {
                if (err) {
                    console.log("下载失败", err);
                } else {
                    // console.log("下载成功", filename, global.gamedata["role"][r]);
                }
            });
        })
    }
    
}

function existsDist(dst, callback) {
    var distdir = dst.substr(0, dst.lastIndexOf("/"));
    if (fs.existsSync(distdir)) {
        callback();
    } else {
        mkPath(distdir.split('/'));
        callback();
    }
};

function mkPath(patharr) {
    for (var i in patharr) {
        var pt = "";
        for (var k = 0; k <= i; k++) {
            pt += patharr[k] + "/";
        }
        if (!fs.existsSync(pt)) {
            fs.mkdirSync(pt);
        }
    }
}
export default {
    hasData,
    getData,
    setData,
    createDB,
    readDB,
    saveDB,
    saveDBto,
    setExportPath,
    exportAllData,
    // leveldata,
    // roledata,
    // getData
    // level: leveldata,
    // role: roledata
}