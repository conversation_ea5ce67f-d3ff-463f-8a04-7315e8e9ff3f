<style>
.levelcontainer {
  list-style: none;
  float: left;
  text-align: left;
  padding-left: 0;
  width: 100%;
  vertical-align: middle;
}
.levelcontainer li {
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 5px 0;
}
.itemname {
  width: 150px;
  float: left;
}
.itemvalue {
  float: left;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.enemyK {
  width: 200px;
  float: left;
  height: 40px;
  overflow: auto;
}
.enemyV {
  width: 100px;
  float: left;
}
.enemyLine {
  width: 100%;
  height: 40px;
  line-height: 40px;
}
</style>
<template>
  <mylistview :formKey="formKey" @setItemData="getItemData">
    <ul class="levelcontainer">
      <li>
        <div class="itemname">商城名：</div>
        <el-input
          v-model="itemData.shopName"
          placeholder="请输入商城名"
          style="width: 50%"
        ></el-input>
      </li>
      <li>
        <div class="itemname">商城物品：</div>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addShop"
        ></i>
        <ul>
          <li
            v-for="(v1, k1) in itemData.shop"
            :key="k1"
            class="itemname"
            slot="title"
            style="height: auto"
          >
            <span>商城物品{{ k1 + 1 }}：</span>
            <i
              class="el-icon-delete"
              style="font-size: 25px; vertical-align: middle"
              @click="removeShop(k1)"
            ></i>
            <!-- <i class="el-icon-more"></i> -->
            <ul>
              <li>
                <span class="enemyK">道具ID</span>
                <el-input
                  v-model="itemData.shop[k1].Id"
                  placeholder="请输入道具ID"
                  style="width: 50%"
                ></el-input>
              </li>
              <li>
                <span class="enemyK">显示图标</span>
                <el-input
                  v-model="itemData.shop[k1].Img"
                  placeholder="请输入图标名"
                  style="width: 50%"
                ></el-input>
              </li>
              <li>
                <span class="enemyK">道具名称：</span>
                <el-input
                  v-model="itemData.shop[k1].name"
                  placeholder="请输入道具名称"
                  style="width: 50%"
                ></el-input>
              </li>
              <li>
                <span class="enemyK">道具数量：</span>
                <el-input
                  v-model="itemData.shop[k1].num"
                  placeholder="请输入道具数量"
                  style="width: 50%"
                  type="number"
                ></el-input>
              </li>
              <li>
                <span class="enemyK">道具描述：</span>
                <el-input
                  v-model="itemData.shop[k1].desc"
                  placeholder="请输入道具描述"
                  style="width: 50%"
                ></el-input>
              </li>
              <li>
                <span class="enemyK">付费类型:</span>
                <!-- <span>{{ itemData.skybox }}</span> -->
                <template>
                  <el-select
                    v-model="itemData.shop[k1].priceType"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </template>
              </li>
              <li>
                <span class="enemyK">价格：</span>
                <el-input
                  v-model="itemData.shop[k1].price"
                  placeholder="请输入价格"
                  style="width: 50%"
                  type="number"
                ></el-input>
              </li>
              <li>
                <span class="enemyK">折扣价：</span>
                <el-input
                  v-model="itemData.shop[k1].discount"
                  placeholder="请输入折扣价"
                  style="width: 50%"
                  type="number"
                ></el-input>
              </li>
              <li>
                <span class="enemyK">限购数量：</span>
                <el-input
                  v-model="itemData.shop[k1].limitBuyNum"
                  placeholder="请输入限购数量"
                  style="width: 50%"
                  type="number"
                ></el-input>
              </li>
              <li>
                <span class="enemyK">赠送数量：</span>
                <el-input
                  v-model="itemData.shop[k1].freeGiveNum"
                  placeholder="请输入赠送数量"
                  style="width: 50%"
                ></el-input>
              </li>
              <li>
                <span class="enemyK">是否自动打开：</span>
                <template>
                  <el-radio-group v-model="itemData.shop[k1].isAutoOpen">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                  </el-radio-group>
                </template>
              </li>
            </ul>
          </li>
        </ul>
      </li>
    </ul>
  </mylistview>
</template>
<script>
import mylistview from "./MyListView.vue";
import Vue from "vue";
export default {
  data() {
    return {
      formKey: "shop",
      curItem: "",
      itemData: {},
      options: [
        {
          value: 0,
          label: "钻石",
        },
        {
          value: 1,
          label: "金币",
        },
        {
          value: 2,
          label: "钱",
        },
        {
          value:3,
          label:"荣誉币"
        }
      ],
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      this.itemData = data;
      if (!this.itemData.shopName) {
        Vue.set(this.itemData, "shopName", "");
      }
      for (let i = 0; i < this.itemData.shop.length; i++) {
        if (!this.itemData.shop[i].hasOwnProperty("isAutoOpen")) {
          Vue.set(this.itemData.shop[i], "isAutoOpen", true);
          // this.itemData.shop[i].isAutoOpen = true;
        }
      }
      console.log("aaaaaaa:", this.itemData);
    },
    addShop: function () {
      let shopItem = {
        Id: "",
        name: "",
        desc: "",
        num: 0,
        priceType: 0,
        price: 0,
        discount: 0,
        limitBuyNum: 0,
        freeGiveNum: 0,
        isAutoOpen: true,
      };
      if (!this.itemData.shop || this.itemData.shop.length == 0) {
        Vue.set(this.itemData, "shop", [shopItem]);
      } else {
        this.itemData.shop.push(shopItem);
      }
    },
    removeShop: function (idx) {
      this.itemData.shop.splice(idx, 1);
    },
  },
};
</script>