<template>
  <mylistview :formKey="formKey" v-on:setItemData="getItemData">
    <ul class="levelcontainer">
      <li>
        <div class="enemyLine">
          <span class="enemyK">名字:</span>
          <el-input
            class="enemyV"
            v-model="itemData.name"
            placeholder="请输入道具名称"
            style="width: 50%"
          ></el-input>
        </div>
      </li>
      <li>
        <div class="enemyLine">
          <span class="enemyK">描述:</span>
          <el-input
            class="enemyV"
            v-model="itemData.desc"
            placeholder="请输入道具描述"
            style="width: 50%"
          ></el-input>
        </div>
      </li>
      <li>
        <div class="itemname">类型:</div>
        <el-dropdown class="itemvalue" @command="selectTypeBox">
          <span class="el-dropdown-link">
            {{ typeStr }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(value, idx) in typeType" :command="idx" :key="idx">{{
              value
            }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </li>
      <li v-if="typeStr === '英雄碎片' || typeStr === '皮肤碎片'">
        <div class="itemname">品质:</div>
        <el-dropdown class="itemvalue" @command="selectQualityBox">
          <span class="el-dropdown-link">
            {{ qualityStr }}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(value, idx) in qualityType"
              :command="idx" :key="idx"
              >{{ value }}</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </li>
      <li
        v-if="
          typeStr === '经验包' ||
          typeStr === '体力包' ||
          typeStr === '进阶卷' ||
          typeStr === '直升卷'
        "
      >
        <div class="enemyLine">
          <span class="enemyK">数值:</span>
          <el-input-number
            class="enemyV"
            v-model.number="itemData.value"
            placeholder="请输入数值"
            style="width: 50%"
          ></el-input-number>
        </div>
      </li>
      <li>
        <div class="enemyLine">
          <span class="enemyK">出售价格:</span>
          <el-input
            class="enemyV"
            v-model="itemData.sellPrice"
            type="number"
            style="width: 50%"
          ></el-input>
        </div>
      </li>
      <li v-if="typeStr === '礼包'">
        <div class="itemname" style="width: 500px">
          礼包内容:(金币-gold,钻石-diamond,体力-power,经验-exp)
        </div>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addArticle"
        ></i>
        <el-collapse>
          <el-collapse-item v-for="(v1, k1) in itemData.Articles" :key="k1">
            <template class="itemname" slot="title">
              道具：
              <el-input
                v-model="v1.id"
                placeholder="请输入道具id"
                style="width: 20%"
              ></el-input>
              <el-input-number
                :min="0"
                v-model="v1.num"
                placeholder="请输入道具数量"
                style="width: 20%"
              ></el-input-number>
              <el-input
                v-model="v1.probability"
                placeholder="请输入道具概率"
                style="width: 20%"
              ></el-input>
              <i
                class="el-icon-delete"
                style="font-size: 25px; vertical-align: middle"
                @click="removeArticle(k1)"
              ></i>
            </template>
          </el-collapse-item>
        </el-collapse>
      </li>
    </ul>
  </mylistview>
</template>
<script>
import mylistview from "./MyListView.vue";
import Vue from "vue";
const qualityType = ["绿色", "蓝色", "紫色", "金色", "红色"];
const typeType = [
  "普通道具",
  "万能碎片",
  "英雄碎片",
  "皮肤碎片",
  "经验包",
  "体力包",
  "进阶卷",
  "直升卷",
  "礼包",
];
export default {
  data() {
    return {
      formKey: "article",
      curItem: "",
      itemData: {},
      qualityStr: "绿色",
      qualityType: qualityType,
      typeStr: "普通道具",
      typeType: typeType,
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      this.itemData = data;
      console.log("getItemData", data);
      if (this.itemData) {
        this.qualityStr = qualityType[this.itemData.quality] || "";
        this.typeStr = typeType[this.itemData.type] || "普通道具";
      } else {
        this.itemData = {};
      }
    },
    selectQualityBox: function (command) {
      // console.log("click on item " + command);
      this.itemData.quality = parseInt(command);
      this.qualityStr = qualityType[command];
    },
    selectTypeBox: function (command) {
      this.itemData.type = parseInt(command);
      this.typeStr = typeType[command];
    },
    addArticle: function () {
      let item = { id: "", num: 0 };
      if (!this.itemData.Articles || this.itemData.Articles.length == 0) {
        Vue.set(this.itemData, "Articles", [item]);
      } else {
        this.itemData.Articles.push(item);
      }
    },
    removeArticle: function (idx) {
      // console.log(idx, this.itemData.MapWaves[idx]);
      var article = this.itemData.Articles[idx];
      this.$confirm("是否删除道具 " + article.id + " 的配置?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.itemData.Articles.splice(idx, 1);
      });
    },
  },
};
</script>