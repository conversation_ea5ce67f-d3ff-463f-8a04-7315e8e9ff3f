<style>
.levelcontainer {
  list-style: none;
  float: left;
  text-align: left;
  padding-left: 0;
  width: 100%;
  vertical-align: middle;
}

.levelcontainer li {
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 5px 0;
}

.itemname {
  width: 150px;
  float: left;
}

.itemvalue {
  float: left;
}

.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}

.el-icon-arrow-down {
  font-size: 12px;
}

.enemyK {
  width: 200px;
  float: left;
  height: 40px;
  overflow: auto;
}

.enemyV {
  width: 100px;
  float: left;
}

.enemyLine {
  width: 100%;
  height: 40px;
  line-height: 40px;
}
</style>
<template>
  <mylistview :formKey="formKey" @setItemData="getItemData">
    <ul class="levelcontainer">
      <li>
        <div class="itemname">名称:</div>
        <el-input
          class="itemvalue"
          v-model="itemData.name"
          placeholder="请输入通行证名称"
          style="width: 50%"
        ></el-input>
      </li>
      <li>
          <div class="itemname">周期（天）：</div>
          <el-input
            class="itemvalue"
            type="number"
            v-model.number="itemData.Duration"
            style="width: 50%"
          ></el-input>
      </li>
      <li>
          <div class="itemname">中级计费点：</div>
          <el-input
            class="itemvalue"
            v-model="itemData.SpecialPayId"
            placeholder="请输入计费点Id（商城表里BattlePass配置）"
            style="width: 50%"
          ></el-input>
      </li>
      <li>
          <div class="itemname">高级计费点：</div>
          <el-input
            class="itemvalue"
            v-model="itemData.GreatPayId"
            placeholder="请输入计费点Id（商城表里BattlePass配置）"
            style="width: 50%"
          ></el-input>
      </li>
      <li>
        <div class="itemname">通行证内容：</div>
        <i class="el-icon-plus" style="font-size: 25px; vertical-align: middle" @click="addList"></i>
        <ul>
          <li v-for="(v1, k1) in itemData.Rewards" :key="k1" class="itemname" slot="title" style="height:220px">
            <span>等级{{ k1 + 1 }}：</span>
            <i class="el-icon-delete" style="font-size: 25px; vertical-align: middle" @click="removeList(k1)"></i>
            <!-- <i class="el-icon-more"></i> -->
            <ul>
              <li>
                <span>需要经验：</span>
                <el-input type="number" v-model.number="itemData.Rewards[k1].Exp" placeholder="请输入领取所需经验" style="width: 50%"></el-input>
              </li>
              <li>
                <span>普通奖励：</span>
                <el-select v-model="itemData.Rewards[k1].NormalID" clearable filterable placeholder="普通ID" class="product-input" allow-create style="width: 30%;">
                    <el-option v-for="(item,index) in CommonlyArticles" :key="index" :label="getArticleName(item)" :value="item"/>
                </el-select>
                <el-input type="number" v-model.number="itemData.Rewards[k1].NormalCount" placeholder="数量" style="width: 30%"></el-input>
                <span>{{getArticleName(itemData.Rewards[k1].NormalID)}}</span>
              </li>
              <li>
                <span>中级奖励：</span>
                <el-select v-model="itemData.Rewards[k1].SpecialID" clearable filterable placeholder="中级ID" class="product-input" allow-create style="width: 30%;">
                    <el-option v-for="(item,index) in CommonlyArticles" :key="index" :label="getArticleName(item)" :value="item"/>
                </el-select>
                <el-input type="number" v-model.number="itemData.Rewards[k1].SpecialCount" placeholder="数量" style="width: 30%"></el-input>
                <span>{{getArticleName(itemData.Rewards[k1].SpecialID)}}</span>
              </li>
              <li>
                <span>高级奖励：</span>
                <el-select v-model="itemData.Rewards[k1].GreatID" clearable filterable placeholder="高级ID" class="product-input" allow-create style="width: 30%;">
                    <el-option v-for="(item,index) in CommonlyArticles" :key="index" :label="getArticleName(item)" :value="item"/>
                </el-select>
                <el-input type="number" v-model.number="itemData.Rewards[k1].GreatCount" placeholder="数量" style="width: 30%"></el-input>
                <span>{{getArticleName(itemData.Rewards[k1].GreatID)}}</span>
              </li>
            </ul>
          </li>
        </ul>
      </li>
    </ul>
  </mylistview>
</template>
<script>
import mylistview from "./MyListView.vue";
import Vue from "vue";
export default {
  data() {
    return {
      formKey: "battlePass",
      curItem: "",
      CommonlyArticles: [],
      itemData: {},
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      // if(data){
      //   for(let i=0,len=data.Rewards.length;i<len;i++){
      //     data.Rewards[i].NormalCount = parseInt(data.Rewards[i].NormalCount);
      //     data.Rewards[i].SpecialCount = parseInt(data.Rewards[i].SpecialCount);
      //     data.Rewards[i].GreatCount = parseInt(data.Rewards[i].GreatCount);
      //   }
      // }
      this.itemData = data;
      this.CommonlyArticles = global.gamedata["CommonlyArticles"] || [];
      console.log("battlepass data: ", data);
      console.log("CommonlyArticles: ", this.CommonlyArticles);
      if (!this.itemData) {
        this.itemData = { };
      }
    },
    addList: function () {
      let battlePass = { 
        Exp: 50,
        NormalID: "Item_029",
        NormalCount: 1,
        SpecialID: "Item_030",
        SpecialCount: 1,
        GreatID: "Item_030",
        GreatCount: 1
      };
      if (!this.itemData.Rewards || this.itemData.Rewards.length == 0) {
        Vue.set(this.itemData, "Rewards", [battlePass]);
      } else {
        this.itemData.Rewards.push(battlePass);
      }
    },
    removeList: function (idx) {
      this.itemData.Rewards.splice(idx, 1);
    },
    getArticleName: function(articleId){
      let _article = global.gamedata["article"][articleId];
      if(!_article) return "";
      return _article.name;
    }
  },
};
</script>