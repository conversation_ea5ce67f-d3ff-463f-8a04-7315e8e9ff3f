<style>
.levelcontainer {
  list-style: none;
  float: left;
  text-align: left;
  padding-left: 0;
  width: 100%;
  vertical-align: middle;
}
.levelcontainer li {
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 5px 0;
}
.itemname {
  width: 150px;
  float: left;
}
.itemvalue {
  float: left;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.enemyK {
  width: 200px;
  float: left;
  height: 40px;
  overflow: auto;
}
.enemyV {
  width: 100px;
  float: left;
}
.enemyLine {
  width: 100%;
  height: 40px;
  line-height: 40px;
}
</style>
<template>
  <mylistview :formKey="formKey" @setItemData="getItemData">
    <ul class="levelcontainer">
      <li>
        <div class="itemname">角色皮肤：</div>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addRoleSkin"
        ></i>
        <el-collapse>
          <el-collapse-item v-for="(v1, k1) in itemData.roleSkins" :key="k1">
            <template class="itemname" slot="title">
              皮肤{{ k1 + 1 }}
              <i
                class="el-icon-delete"
                style="font-size: 25px; vertical-align: middle"
                @click="removeRoleSkin(k1)"
              ></i>
            </template>
            <ul>
              <li>
                <span class="enemyK">角色皮肤ID：</span>
                <el-input
                  v-model="v1.roleroleSkinId"
                  placeholder="请输入角色皮肤ID"
                  style="width: 50%"
                ></el-input>
              </li>
              <li>
                <span class="enemyK">角色皮肤名称：</span>
                <el-input
                  v-model="v1.roleSkinName"
                  placeholder="请输入角色皮肤名称"
                  style="width: 50%"
                ></el-input>
              </li>
              <li>
                <span class="enemyK">角色皮肤头像ID：</span>
                <el-input
                  v-model="v1.roleSkinHeadPortrait"
                  placeholder="请输入角色皮肤头像ID"
                  style="width: 50%"
                ></el-input>
              </li>
              <li>
                <span class="enemyK">角色皮肤品质：(占位)</span>
              </li>
              <li>
                <span class="enemyK">角色皮肤被动技能名称：</span>
                <el-input
                  v-model="v1.roleSkinPassiveSkillName"
                  placeholder="请输入角色皮肤被动技能名称"
                  style="width: 50%"
                ></el-input>
              </li>
              <li>
                <span class="enemyK">角色皮肤被动技能描述：</span>
                <el-input
                  v-model="v1.roleSkinPassiveSkillDesc"
                  placeholder="请输入角色皮肤被动技能名称"
                  style="width: 50%"
                ></el-input>
              </li>
              <li>
                <span class="enemyK">角色皮肤被动技能BuffID：</span>
                <el-input
                  v-model="v1.roleSkinPassiveBuffId"
                  placeholder="请输入角色皮肤被动技能名称"
                  style="width: 50%"
                ></el-input>
              </li>
              <li>
                <span class="enemyK">角色皮肤属性加成：</span>
                <el-input-number
                  v-model="v1.roleSkinAttributeAddition"
                  placeholder="请输入角色皮肤被动技能名称"
                  style="width: 50%"
                ></el-input-number>
              </li>
              <li>
                <span class="enemyK">角色技能图标开头：</span>
                <el-input
                  v-model="v1.roleSkinSkillIcon"
                  placeholder="请输入角色技能图标开头"
                  style="width: 50%"
                ></el-input>
              </li>
              <li>
                <span class="enemyK">角色皮肤主动技能：</span>
                <ul>
                  <li
                    v-for="(v2, k2) in v1.roleSkinSkills"
                    :key="k2"
                    class="itemname"
                    style="height: 100px"
                  >
                    <span class="enemyK">技能{{ k2 + 1 }}名称</span>
                    <el-input
                      v-model="v2.activeSkillName"
                      placeholder="请输入角色皮肤技能名称"
                      style="width: 50%"
                    ></el-input>
                    <br />
                    <span class="enemyK">技能{{ k2 + 1 }}描述</span>
                    <el-input
                      v-model="v2.activeSkillDesc"
                      placeholder="请输入角色皮肤技能描述"
                      style="width: 50%"
                    ></el-input>
                  </li>
                </ul>
              </li>
              <li style="height: auto">
                <div class="itemname">解锁条件：</div>
                <i
                  class="el-icon-plus"
                  style="font-size: 25px; vertical-align: middle"
                  @click="addUnlockCondition(v1)"
                ></i>
                <el-collapse>
                  <el-collapse-item
                    v-for="(v2, k2) in v1.UnlockConditions"
                    :key="k2"
                  >
                    <template class="itemname" slot="title">
                      条件{{ k2 + 1 }}
                      <i
                        class="el-icon-plus"
                        style="font-size: 25px; vertical-align: middle"
                        @click="addUnlockConditionFall(v2)"
                      ></i>
                      <i
                        class="el-icon-delete"
                        style="font-size: 25px; vertical-align: middle"
                        @click="removeUnlockCondition(v1.UnlockConditions,k2)"
                      ></i>
                    </template>
                    <ul>
                      <li
                        v-for="(v3, k3) in v2"
                        :key="k3"
                        class="itemname"
                        slot="title"
                        style="height: auto"
                      >
                        <span>物品{{ k3 + 1 }}：</span>
                        <i
                          class="el-icon-delete"
                          style="font-size: 25px; vertical-align: middle"
                          @click="removeUnlockConditionFall(v2, k3)"
                        ></i>
                        <ul>
                          <li>
                            <span class="enemyK">物品类型：</span>
                            <template>
                              <el-select v-model="v3.type" placeholder="请选择" @change="ClearData(v3,v3.type)">
                                <el-option
                                  v-for="item in options"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value"
                                >
                                </el-option>
                              </el-select>
                            </template>
                          </li>
                          <li v-if="v3.type == 0">
                            <span class="enemyK">物品ID：</span>
                            <el-input
                              v-model="v3.itemID"
                              placeholder="请输入物品ID"
                              style="width: 50%"
                            ></el-input>
                          </li>
                          <li>
                            <span class="enemyK">数量：</span>
                            <el-input-number
                              class="enemyV"
                              v-model="v3.nums"
                              style="width: 50%"
                            ></el-input-number>
                          </li>
                        </ul>
                      </li>
                    </ul>
                  </el-collapse-item>
                </el-collapse>
              </li>
            </ul>
          </el-collapse-item>
        </el-collapse>
      </li>
    </ul>
  </mylistview>
</template>
<script>
import mylistview from "./MyListView.vue";
import Vue from "vue";
export default {
  data() {
    return {
      formKey: "roleSkin",
      curItem: "",
      itemData: {},
      options: [
        {
          value: 0,
          label: "道具",
        },
        {
          value: 1,
          label: "钱",
        },
      ],
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      this.itemData = data;
    },
    addRoleSkin: function () {
      let roleSkin = {
        roleroleSkinId: "",
        roleSkinName: "",
        roleSkinHeadPortrait: "",
        roleSkinQuality: 0,
        roleSkinPassiveSkillName: "",
        roleSkinPassiveSkillDesc: "",
        roleSkinPassiveBuffId: "",
        roleSkinAttributeAddition: 0,
        roleSkinSkillIcon: "",
        roleSkinSkills: [
          {
            activeSkillName: "",
            activeSkillDesc: "",
          },
          {
            activeSkillName: "",
            activeSkillDesc: "",
          },
          {
            activeSkillName: "",
            activeSkillDesc: "",
          },
          {
            activeSkillName: "",
            activeSkillDesc: "",
          },
          {
            activeSkillName: "",
            activeSkillDesc: "",
          },
        ],
      };
      if (!this.itemData.roleSkins || this.itemData.roleSkins.length == 0) {
        Vue.set(this.itemData, "roleSkins", [roleSkin]);
      } else {
        this.itemData.roleSkins.push(roleSkin);
      }
    },
    removeRoleSkin: function (idx) {
      this.itemData.roleSkins.splice(idx, 1);
    },
    addUnlockCondition: function (roleSkin) {
      let UnlockCondition = [{ itemID: "", nums: 0, type: 0 }];
      if (
        !roleSkin.UnlockConditions ||
        roleSkin.UnlockConditions.length == 0
      ) {
        Vue.set(roleSkin, "UnlockConditions", [UnlockCondition]);
      } else {
        roleSkin.UnlockConditions.push(UnlockCondition);
      }
    },
    removeUnlockCondition: function (UnlockConditions,idx) {
      UnlockConditions.splice(idx, 1);
    },
    addUnlockConditionFall: function (UnlockCondition) {
      let UnlockConditionFall = {
        itemID: "",
        nums: 0,
        type: 0,
      };
      UnlockCondition.push(UnlockConditionFall);
    },
    removeUnlockConditionFall: function (UnlockCondition,index) {
      UnlockCondition.splice(index, 1);
    },
    ClearData: function (UnlockConditionFall,type) {
      if(type==0){
        Vue.set(UnlockConditionFall,"itemID","");
      }
      else{
        Vue.delete(UnlockConditionFall,"itemID");
      }
    },
  },
};
</script>