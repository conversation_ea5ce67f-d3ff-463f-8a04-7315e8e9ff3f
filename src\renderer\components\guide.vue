<style>
.levelcontainer {
  list-style: none;
  float: left;
  text-align: left;
  padding-left: 0;
  width: 100%;
  vertical-align: middle;
}
.levelcontainer li {
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 5px 0;
}
.itemname {
  width: 150px;
  float: left;
}
.itemvalue {
  float: left;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.enemyK {
  width: 200px;
  float: left;
  height: 40px;
  overflow: auto;
}
.enemyV {
  width: 100px;
  float: left;
}
.enemyLine {
  width: 100%;
  height: 40px;
  line-height: 40px;
}
</style>
<template>
  <mylistview :formKey="formKey" @setItemData="getItemData">
    <ul class="levelcontainer">
      <li>
        <div class="itemname">触发条件类型:</div>
        <!-- <span>{{ itemData.skybox }}</span> -->
        <template>
          <el-select
            v-model="itemData.condition"
            placeholder="请选择"
            @change="ClearData(itemData.condition)"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
      </li>
      <li>
        <div class="itemname">条件说明:</div>
        <el-input
          v-model="itemData.conditionDesc"
          class="enemyV"
          style="width: 50%"
        >
        </el-input>
      </li>
      <ul class="levelcontainer" v-if="itemData.condition == 2">
        <li v-for="(value, key) in getPropConfig" :key="key">
          <div class="enemyLine">
            <span class="itemname">
              {{ value.name }}
            </span>
            <el-input
              v-if="value.type == 'string'"
              v-model="itemData[value.config]"
              class="enemyV"
              style="width: 50%"
            >
            </el-input>
            <el-input
              v-else
              v-model.number="itemData[value.config]"
              type="number"
              class="enemyV"
              style="width: 50%"
            >
            </el-input>
          </div>
        </li>
      </ul>
      <ul class="levelcontainer" v-if="itemData.condition == 1">
        <li v-for="(value, key) in passLevelConfig" :key="key">
          <div class="enemyLine">
            <span class="itemname">
              {{ value.name }}
            </span>
            <el-input
              v-if="value.type == 'string'"
              v-model="itemData[value.config]"
              class="enemyV"
              style="width: 50%"
            >
            </el-input>
            <el-input
              v-else
              v-model.number="itemData[value.config]"
              type="number"
              class="enemyV"
              style="width: 50%"
            >
            </el-input>
          </div>
        </li>
      </ul>
      <li>
        <div class="itemname">操作步骤：</div>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addpGuide"
        ></i>
        <ul>
          <li
            v-for="(v1, k1) in itemData.guideSteps"
            :key="k1"
            class="itemname"
            slot="title"
          >
            <span>步骤{{ k1 + 1 }}：</span>
            <!-- <i class="el-icon-more"></i> -->
            <template>
              <el-select
                v-model="v1.guideStepType"
                placeholder="请选择"
                @change="ChangeStepData(v1, v1.guideStepType)"
              >
                <el-option
                  v-for="item in stepOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </template>

            <el-input
              v-if="v1.guideStepType == 0 || v1.guideStepType == 5"
              v-model="v1.guideClickStepPath"
              placeholder="请输入点击路径"
              style="width: 50%"
            ></el-input>
            <el-input
              v-if="v1.guideStepType == 1"
              v-model="v1.guidePlotId"
              placeholder="请输入剧情文本名称"
              style="width: 50%"
            ></el-input>

            <el-input
              v-if="v1.guideStepType == 2"
              v-model="v1.guidePopupPath"
              placeholder="请输入弹窗路径"
              style="width: 50%"
            ></el-input>
            <el-input
              v-if="v1.guideStepType == 3"
              v-model="v1.guideItemId"
              placeholder="请输入道具ID"
              style="width: 24%"
            ></el-input>
            <el-input-number
              v-if="v1.guideStepType == 3"
              v-model.number="v1.guideItemNum"
              placeholder="请输入道具数量"
              type="number"
              style="width: 24%"
            ></el-input-number>
            <el-input
              v-if="v1.guideStepType == 4"
              v-model="v1.guideHeroID"
              placeholder="请输入英雄ID"
              style="width: 24%"
            ></el-input>
            <el-input-number
              v-if="v1.guideStepType == 6"
              v-model.number="v1.guideDelay"
              placeholder="请输入延迟时间"
              type="number"
              style="width: 24%"
            ></el-input-number>
            <el-input
              v-if="v1.guideStepType == 7 || v1.guideStepType == 8"
              v-model="v1.guideEvent"
              placeholder="请输入事件ID"
              style="width: 24%"
            ></el-input>
            <i
              class="el-icon-delete"
              style="font-size: 25px; vertical-align: middle"
              @click="removeGuide(k1)"
            ></i>
          </li>
        </ul>
      </li>
    </ul>
  </mylistview>
</template>
<script>
import mylistview from "./MyListView.vue";
import Vue from "vue";
export default {
  data() {
    return {
      formKey: "guide",
      curItem: "",
      itemData: {},
      options: [
        {
          value: 0,
          label: "回到首页",
        },
        {
          value: 1,
          label: "通过关卡",
        },
        {
          value: 2,
          label: "获得道具",
        },
      ],
      stepOptions: [
        {
          value: 0,
          label: "点击",
        },
        {
          value: 1,
          label: "对话",
        },
        {
          value: 2,
          label: "弹窗",
        },
        {
          value: 3,
          label: "获得道具",
        },
        {
          value: 4,
          label: "获得英雄",
        },
        {
          value: 5,
          label: "按下",
        },
        {
          value: 6,
          label: "延迟",
        },
        {
          value: 7,
          label: "发送事件",
        },
        {
          value: 8,
          label: "等待事件",
        },
      ],
      getPropConfig: [
        // { name: "道具ID", config: "propID", type: "string" },
        // { name: "道具数量", config: "propNumbers" },
      ],
      passLevelConfig: [
        { name: "关卡名称", config: "levelName", type: "string" },
      ],
      stepOption: "",
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      this.itemData = data;
    },
    ClearData: function (value) {
      console.log("vavalue", value);
      Vue.delete(this.itemData, "levelName");
      Vue.delete(this.itemData, "propID");
      Vue.delete(this.itemData, "propNumbers");
      if (value == 0) {
      }
      if (value == 2) {
        // delete this.itemData.levelName
        Vue.set(this.itemData, "propID");
        Vue.set(this.itemData, "propNumbers");
      } else if (value == 1) {
        // delete this.itemData.propID;
        // delete this.itemData.propNumbers;
        Vue.set(this.itemData, "levelName");
      }
    },
    addpGuide: function () {
      let guideSteps = { guideStepType: 0, guideClickStepPath: "" };
      if (!this.itemData.guideSteps || this.itemData.guideSteps.length == 0) {
        Vue.set(this.itemData, "guideSteps", [guideSteps]);
      } else {
        this.itemData.guideSteps.push(guideSteps);
      }
    },
    removeGuide: function (idx) {
      this.itemData.guideSteps.splice(idx, 1);
    },
    ChangeStepData: function (object, value) {
      Vue.delete(object, "guidePlotId");
      Vue.delete(object, "guidePopupPath");
      Vue.delete(object, "guideItemId");
      Vue.delete(object, "guideItemNum");
      Vue.delete(object, "guideHeroID");
      Vue.delete(object, "guideDelay");
      Vue.delete(object, "guideEvent");
      Vue.delete(object, "guideClickStepPath");
      if (value == 0) {
        object = { guideStepType: 0, guideClickStepPath: "" };
      } else if (value == 1) {
        object = { guideStepType: 1, guidePlotId: "" };
      } else if (value == 2) {
        object = { guideStepType: 2, guidePopupPath: "" };
      } else if (value == 3) {
        object = {
          guideStepType: 3,
          guideItemId: "",
          guideItemNum: 0,
        };
      } else if (value == 4) {
        object = { guideStepType: 4, guideHeroID: "" };
      } else if (value == 5) {
        object = { guideStepType: 5, guideClickStepPath: "" };
      } else if (value == 6) {
        object = { guideStepType: 6, guideDelay: 0 };
      } else if (value == 7) {
        object = { guideStepType: 7, guideEvent: "" };
      } else if (value == 8) {
        object = { guideStepType: 8, guideEvent: "" };
      }
    },
  },
};
</script>