<style>
.levelcontainer {
  list-style: none;
  float: left;
  text-align: left;
  padding-left: 0;
  width: 100%;
  vertical-align: middle;
}
.levelcontainer li {
  width: 100%;
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 5px 0;
}
.itemname {
  width: 150px;
  float: left;
}
.itemvalue {
  float: left;
}
.el-dropdown-link {
  cursor: pointer;
  color: #409eff;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.enemyK {
  width: 200px;
  float: left;
  height: 40px;
  overflow: auto;
}
.enemyV {
  width: 100px;
  float: left;
}
.enemyLine {
  width: 100%;
  height: 40px;
  line-height: 40px;
}
</style>
<template>
  <mylistview :formKey="formKey" @setItemData="getItemData">
    <ul class="levelcontainer">
      <li>
        <div class="itemname">成就类型:</div>
        <!-- <span>{{ itemData.skybox }}</span> -->
        <template>
          <el-select v-model="itemData.type" placeholder="请选择">
            <el-option
              v-for="item in types"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
      </li>
      <li>
        <div class="itemname">触发类型:</div>
        <!-- <span>{{ itemData.skybox }}</span> -->
        <template>
          <el-select
            v-model="itemData.achievementType"
            placeholder="请选择"
            @change="ClearData(itemData.achievementType)"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
      </li>
      <li>
        <div class="itemname">成就名称:</div>
        <el-input
          v-model="itemData.achievementName"
          class="enemyV"
          style="width: 50%"
        >
        </el-input>
      </li>
      <li>
        <div class="itemname">描述:</div>
        <el-input
          v-model="itemData.achievementDesc"
          class="enemyV"
          style="width: 50%"
        >
        </el-input>
      </li>
      <li
        v-if="
          itemData.achievementType == 0 ||
          itemData.achievementType == 2 ||
          itemData.achievementType == 3
        "
      >
        <div class="itemname">品质:</div>
        <template>
          <el-select v-model="itemData.quality" placeholder="请选择">
            <el-option
              v-for="item in qualityType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
      </li>
      <li
        v-if="
          itemData.achievementType == 0 ||
          itemData.achievementType == 2 ||
          itemData.achievementType == 3
        "
      >
        <div class="itemname">物品类型:</div>
        <template>
          <el-select v-model="itemData.itemType" placeholder="请选择">
            <el-option
              v-for="item in itemType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </template>
      </li>
      <li
        v-if="
          itemData.achievementType == 0 ||
          itemData.achievementType == 2 ||
          itemData.achievementType == 3
        "
      >
        <div class="itemname">物品等级:</div>
        <el-input-number
          class="enemyV"
          v-model="itemData.itemLevel"
          style="width: 50%"
        ></el-input-number>
      </li>
      <li
        v-if="
          itemData.achievementType == 0 ||
          itemData.achievementType == 2 ||
          itemData.achievementType == 3
        "
      >
        <div class="itemname">物品数量:</div>
        <el-input-number
          class="enemyV"
          v-model="itemData.itemNums"
          style="width: 50%"
        ></el-input-number>
      </li>
      <li
        v-if="itemData.achievementType == 4">
        <div class="itemname">观看次数:</div>
        <el-input-number
          class="enemyV"
          v-model="itemData.adVideoWatchNums"
          style="width: 50%"
        ></el-input-number>
      </li>
      <li v-if="itemData.achievementType == 1">
        <div class="itemname">章节:</div>
        <el-input-number
          class="enemyV"
          v-model="itemData.chapter"
          style="width: 50%"
        ></el-input-number>
      </li>
      <li>
        <div class="itemname">添加奖励：</div>
        <i
          class="el-icon-plus"
          style="font-size: 25px; vertical-align: middle"
          @click="addReward"
        ></i>
        <ul>
          <li
            v-for="(v1, k1) in itemData.rewards"
            :key="k1"
            class="itemname"
            slot="title"
            style="height: auto"
          >
            <span>物品{{ k1 + 1 }}：</span>
            <i
              class="el-icon-delete"
              style="font-size: 25px; vertical-align: middle"
              @click="removeReward(k1)"
            ></i>
            <!-- <i class="el-icon-more"></i> -->
            <ul>
              <li>
                <span class="enemyK">奖励类型：</span>
                <template>
                  <el-select v-model="v1.rewardType" placeholder="请选择">
                    <el-option
                      v-for="item in rewardTypes"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </template>
              </li>
              <li>
                <span class="enemyK">奖励数量：</span>
                <el-input-number
                  class="enemyV"
                  v-model="v1.nums"
                  style="width: 50%"
                ></el-input-number>
              </li>
            </ul>
          </li>
        </ul>
      </li>
    </ul>
  </mylistview>
</template>
<script>
import mylistview from "./MyListView.vue";
import Vue from "vue";
export default {
  data() {
    return {
      formKey: "achievement",
      curItem: "",
      itemData: {},
      options: [
        {
          value: 0,
          label: "获得",
        },
        {
          value: 1,
          label: "通关",
        },
        {
          value: 2,
          label: "装备上",
        },
        {
          value: 3,
          label: "升级",
        },
        {
          value: 4,
          label: "广告",
        },
      ],
      types: [
        {
          value: 0,
          label: "每日",
        },
        {
          value: 1,
          label: "事件",
        },
        {
          value: 2,
          label: "英雄",
        },
      ],
      rewardTypes: [
        {
          value: 0,
          label: "金币",
        },
        {
          value: 1,
          label: "钻石",
        },
        {
          value: 2,
          label: "体力",
        },
      ],
      qualityType: [
        {
          value: 0,
          label: "绿色",
        },
        {
          value: 1,
          label: "蓝色",
        },
        {
          value: 2,
          label: "紫色",
        },
        {
          value: 3,
          label: "橙色",
        },
        {
          value: 4,
          label: "红色",
        },
      ],
      itemType: [
        {
          value: 0,
          label: "武器",
        },
        {
          value: 1,
          label: "道具",
        },
        {
          value: 2,
          label: "源晶",
        },
        {
          value: 3,
          label: "角色",
        },
      ],
      getPropConfig: [
        // { name: "道具ID", config: "propID", type: "string" },
        // { name: "道具数量", config: "propNumbers" },
      ],
      passLevelConfig: [
        { name: "关卡名称", config: "levelName", type: "string" },
      ],
      stepOption: "",
    };
  },
  components: {
    mylistview,
  },
  methods: {
    getItemData: function (data) {
      this.itemData = data;
    },
    ClearData: function (index) {
      if (index == 0 || index == 2 || index == 3) {
        Vue.delete(this.itemData, "Chapters");
        Vue.delete(this.itemData, "chapter");
        Vue.delete(this.itemData, "adVideoWatchNums");
        Vue.set(this.itemData, "quality", 0);
        Vue.set(this.itemData, "itemType", 0);
        Vue.set(this.itemData, "itemNums", 1);
        Vue.set(this.itemData, "itemLevel", 1);
      } else if (index == 1) {
        Vue.delete(this.itemData, "quality");
        Vue.delete(this.itemData, "itemType");
        Vue.delete(this.itemData, "itemNums");
        Vue.delete(this.itemData, "itemLevel");
        Vue.delete(this.itemData, "adVideoWatchNums");
        Vue.set(this.itemData, "chapter", 1);
      } else if (index == 4) {
        Vue.delete(this.itemData, "quality");
        Vue.delete(this.itemData, "itemType");
        Vue.delete(this.itemData, "itemNums");
        Vue.delete(this.itemData, "itemLevel");
        Vue.delete(this.itemData, "Chapters");
        Vue.delete(this.itemData, "chapter");
        Vue.set(this.itemData,"adVideoWatchNums",1);
      }
    },
    addReward: function () {
      let reward = { rewardType: 0, nums: 0 };
      if (!this.itemData.rewards || this.itemData.rewards.length == 0) {
        Vue.set(this.itemData, "rewards", [reward]);
      } else {
        this.itemData.rewards.push(reward);
      }
    },
    removeReward: function (idx) {
      this.itemData.rewards.splice(idx, 1);
    },
    // addRewardFall: function (index) {
    //   let reward = {
    //     rewardType: 0,
    //     nums: 0,
    //   };
    //   this.itemData.rewards[index].push(reward);
    // },
    // removeRewardFall: function (index1, index2) {
    //   this.itemData.rewards[index1].splice(index2, 1);
    // },
    addChapter: function () {
      let chapter = [{ rewardType: 0, nums: 0 }];
      if (!this.itemData.Chapters || this.itemData.Chapters.length == 0) {
        Vue.set(this.itemData, "Chapters", [chapter]);
      } else {
        this.itemData.Chapters.push(chapter);
      }
    },
    removeChapter: function (idx) {
      this.itemData.Chapters.splice(idx, 1);
    },
    addChapterFall: function (index) {
      let chapterFall = { rewardType: 0, nums: 0 };
      this.itemData.Chapters[index].push(chapterFall);
    },
    removeChapterFall: function (index1, index2) {
      this.itemData.Chapters[index1].splice(index2, 1);
    },
  },
};
</script>